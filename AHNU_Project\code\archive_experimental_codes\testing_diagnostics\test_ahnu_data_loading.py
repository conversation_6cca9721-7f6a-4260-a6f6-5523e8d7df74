#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(r'E:\gitrepo\PINN4SOH\neural_operator_experiment')

from main_deeponet_bnn_pinn_v3 import AHNUDataProcessor
import argparse

def test_ahnu_data_loading():
    """测试AHNU数据加载功能"""
    
    print("🧪 测试AHNU数据加载功能")
    print("="*50)
    
    # 创建参数
    class Args:
        normalization_method = 'z-score'
        batch_size = 32
    
    args = Args()
    
    # 数据文件路径
    data_file = r'E:\gitrepo\PINN4SOH\data\AHNU\0.1-0.5A g-1 <EMAIL>'
    
    try:
        # 创建数据处理器
        processor = AHNUDataProcessor(data_file, args)
        
        # 加载和处理数据
        X1, X2, Y1, Y2 = processor.load_and_process_data()
        
        print(f"✅ 数据加载成功！")
        print(f"  - X1形状 (第一时刻特征): {X1.shape}")
        print(f"  - X2形状 (第二时刻特征): {X2.shape}") 
        print(f"  - Y1形状 (第一时刻SOH): {Y1.shape}")
        print(f"  - Y2形状 (第二时刻SOH): {Y2.shape}")
        
        print(f"\\n📊 17维特征范围:")
        for i in range(17):
            print(f"  特征 {i+1:2d}: [{X1[:, i].min():.4f}, {X1[:, i].max():.4f}]")
        
        print(f"\\n🔋 SOH值范围:")
        print(f"  Y1: [{Y1.min():.4f}, {Y1.max():.4f}]")
        print(f"  Y2: [{Y2.min():.4f}, {Y2.max():.4f}]")
        
        print(f"\\n📈 数据统计:")
        print(f"  - 样本对数量: {len(X1)}")
        print(f"  - 特征维度: {X1.shape[1]}")
        print(f"  - SOH平均值: {Y1.mean():.4f}")
        print(f"  - SOH标准差: {Y1.std():.4f}")
        
        print(f"\\n🎯 测试结论:")
        if X1.shape[1] == 17:
            print(f"  ✅ 成功提取17维特征")
        else:
            print(f"  ❌ 特征维度错误: {X1.shape[1]} != 17")
            
        if 0.1 <= Y1.min() and Y1.max() <= 1.2:
            print(f"  ✅ SOH值在合理范围内")
        else:
            print(f"  ⚠️  SOH值范围需要检查")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_ahnu_data_loading()
    
    if success:
        print(f"\\n🎉 AHNU数据加载测试通过！")
        print(f"💡 可以继续运行完整的训练脚本")
    else:
        print(f"\\n💥 测试失败，请检查数据文件和代码")