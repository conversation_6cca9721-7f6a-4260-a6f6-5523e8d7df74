#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AHNU电池SOH预测最优解决方案
使用3个最相关特征的线性回归模型
数据路径：绝对路径版本
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
import matplotlib.pyplot as plt
import os
import joblib

class AHNUOptimalProcessor:
    """AHNU数据最优处理器 - 3特征版本"""
    
    def __init__(self):
        # 使用绝对路径
        self.data_path = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
        self.scaler = StandardScaler()
        self.best_features = None
        
    def load_and_analyze_data(self):
        """加载数据并分析最优特征"""
        print(f"📊 加载AHNU数据: {self.data_path}")
        
        df = pd.read_excel(self.data_path)
        print(f"原始数据形状: {df.shape}")
        
        # SOH计算
        discharge_capacity = df['放电比容量(mAh/g)'].values
        initial_capacity = discharge_capacity[0]
        soh = discharge_capacity / initial_capacity
        soh = np.clip(soh, 0.1, 1.2)
        
        # 候选特征集（9个原始特征）
        candidate_features = {
            '放电比容量': df['放电比容量(mAh/g)'].values,
            '充电比容量': df['充电比容量(mAh/g)'].values,
            '充放电效率': df['充放电效率(%)'].values,
            '循环号': df['循环号'].values,
            '中值电压': df['中值电压(V)'].values,
            '充电时间': df['充电时间(h)'].values,
            '放电时间': df['放电时间(h)'].values,
            '充电平均电压': df['充电平均电压(V)'].values,
            '放电平均电压': df['放电平均电压(V)'].values,
        }
        
        print(f"\n🔍 特征与SOH相关性分析:")
        # 分析每个特征与SOH的相关性
        correlations = {}
        for name, values in candidate_features.items():
            corr = np.corrcoef(values, soh)[0, 1]
            correlations[name] = abs(corr)
            print(f"  {name}: 相关性 = {corr:+.4f}")
        
        # 选择最相关的3个特征
        sorted_features = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
        top_features = [item[0] for item in sorted_features[:3]]
        
        print(f"\n🎯 选择的最佳3个特征: {top_features}")
        
        # 构建特征矩阵
        X = np.column_stack([candidate_features[name] for name in top_features])
        self.best_features = top_features
        
        print(f"📊 最终特征矩阵形状: {X.shape}")
        print(f"📊 SOH范围: [{soh.min():.4f}, {soh.max():.4f}]")
        
        return X, soh
    
    def prepare_data_for_training(self, X, y):
        """准备训练数据"""
        # 时间序列划分 - 前80%训练，后20%测试
        split_idx = int(len(X) * 0.8)
        
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        print(f"📊 数据划分: 训练集={len(X_train)}, 测试集={len(X_test)}")
        
        return X_train_scaled, X_test_scaled, y_train, y_test

class AHNUOptimalModel:
    """AHNU最优模型集成"""
    
    def __init__(self):
        self.models = {}
        self.best_model_name = None
        self.best_model = None
        
    def train_all_models(self, X_train, y_train, X_test, y_test):
        """训练所有候选模型"""
        
        print(f"\n🏋️  训练最优模型集合 (特征维度: {X_train.shape[1]})")
        print("="*50)
        
        results = {}
        
        # 1. 线性回归
        print("📊 1. 线性回归")
        lr = LinearRegression()
        lr.fit(X_train, y_train)
        y_pred_lr = lr.predict(X_test)
        
        mse_lr = np.mean((y_test - y_pred_lr) ** 2)
        mae_lr = np.mean(np.abs(y_test - y_pred_lr))
        r2_lr = 1 - np.sum((y_test - y_pred_lr) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
        
        results['Linear Regression'] = {'model': lr, 'pred': y_pred_lr, 'MSE': mse_lr, 'MAE': mae_lr, 'R2': r2_lr}
        self.models['Linear Regression'] = lr
        
        print(f"  MSE: {mse_lr:.8f}, MAE: {mae_lr:.6f}, R²: {r2_lr:.6f}")
        
        # 2. 随机森林
        print("🌲 2. 优化随机森林")
        rf = RandomForestRegressor(
            n_estimators=50,
            max_depth=3,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42
        )
        rf.fit(X_train, y_train)
        y_pred_rf = rf.predict(X_test)
        
        mse_rf = np.mean((y_test - y_pred_rf) ** 2)
        mae_rf = np.mean(np.abs(y_test - y_pred_rf))
        r2_rf = 1 - np.sum((y_test - y_pred_rf) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
        
        results['Random Forest'] = {'model': rf, 'pred': y_pred_rf, 'MSE': mse_rf, 'MAE': mae_rf, 'R2': r2_rf}
        self.models['Random Forest'] = rf
        
        print(f"  MSE: {mse_rf:.8f}, MAE: {mae_rf:.6f}, R²: {r2_rf:.6f}")
        
        # 3. 超轻量神经网络
        print("🧠 3. 超轻量神经网络")
        
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        class UltraLightNN(nn.Module):
            def __init__(self, input_dim):
                super().__init__()
                self.net = nn.Sequential(
                    nn.Linear(input_dim, 8),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(8, 1)
                )
            
            def forward(self, x):
                return torch.sigmoid(self.net(x))
        
        # 数据转换
        X_train_torch = torch.FloatTensor(X_train).to(device)
        y_train_torch = torch.FloatTensor(y_train).unsqueeze(1).to(device)
        X_test_torch = torch.FloatTensor(X_test).to(device)
        
        # 训练
        unn = UltraLightNN(X_train.shape[1]).to(device)
        optimizer = torch.optim.Adam(unn.parameters(), lr=0.005, weight_decay=0.05)
        criterion = nn.MSELoss()
        
        unn.train()
        for epoch in range(100):
            optimizer.zero_grad()
            outputs = unn(X_train_torch)
            loss = criterion(outputs, y_train_torch)
            loss.backward()
            optimizer.step()
        
        # 测试
        unn.eval()
        with torch.no_grad():
            y_pred_unn = unn(X_test_torch).cpu().numpy().flatten()
        
        mse_unn = np.mean((y_test - y_pred_unn) ** 2)
        mae_unn = np.mean(np.abs(y_test - y_pred_unn))
        r2_unn = 1 - np.sum((y_test - y_pred_unn) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
        
        results['Ultra Light NN'] = {'model': unn, 'pred': y_pred_unn, 'MSE': mse_unn, 'MAE': mae_unn, 'R2': r2_unn}
        self.models['Ultra Light NN'] = unn
        
        print(f"  MSE: {mse_unn:.8f}, MAE: {mae_unn:.6f}, R²: {r2_unn:.6f}")
        
        # 选择最佳模型
        best_r2 = max(results[name]['R2'] for name in results)
        for name, metrics in results.items():
            if metrics['R2'] == best_r2:
                self.best_model_name = name
                self.best_model = self.models[name]
                break
        
        print(f"\n🏆 最佳模型: {self.best_model_name} (R² = {best_r2:.6f})")
        
        return results
    
    def get_feature_importance(self, processor):
        """获取特征重要性"""
        if self.best_model_name == 'Linear Regression':
            coef = self.best_model.coef_
            importance = abs(coef) / sum(abs(coef))
        elif self.best_model_name == 'Random Forest':
            importance = self.best_model.feature_importances_
        else:
            importance = np.ones(len(processor.best_features)) / len(processor.best_features)
        
        print(f"\n📊 {self.best_model_name} 特征重要性:")
        for name, imp in zip(processor.best_features, importance):
            print(f"  {name}: {imp:.4f}")
        
        return dict(zip(processor.best_features, importance))

def plot_optimal_results(results, y_test, processor, save_dir):
    """绘制最优结果"""
    
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 找到最佳模型
    best_r2 = max(results[name]['R2'] for name in results)
    best_name = None
    for name, metrics in results.items():
        if metrics['R2'] == best_r2:
            best_name = name
            break
    
    # 1. 最佳模型预测对比
    ax = axes[0, 0]
    cycles = range(len(y_test))
    y_pred_best = results[best_name]['pred']
    
    ax.plot(cycles, y_test, label='True SOH', color='black', linewidth=2, marker='o', markersize=3)
    ax.plot(cycles, y_pred_best, label=f'{best_name}', color='red', linewidth=2, marker='s', markersize=3)
    ax.set_xlabel('Test Sample Index')
    ax.set_ylabel('SOH')
    ax.set_title(f'Best Model: {best_name} (R²={best_r2:.4f})')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. 误差分析
    ax = axes[0, 1]
    errors = np.abs(y_test - y_pred_best)
    ax.plot(cycles, errors, color='green', linewidth=2, marker='d', markersize=3)
    ax.set_xlabel('Test Sample Index')
    ax.set_ylabel('Absolute Error')
    ax.set_title(f'Prediction Error (MAE: {results[best_name]["MAE"]:.6f})')
    ax.grid(True, alpha=0.3)
    
    # 3. 散点图
    ax = axes[1, 0]
    ax.scatter(y_test, y_pred_best, alpha=0.7, s=50, color='blue')
    min_val, max_val = min(y_test.min(), y_pred_best.min()), max(y_test.max(), y_pred_best.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.set_title('True vs Predicted')
    ax.grid(True, alpha=0.3)
    
    # 4. 特征重要性 (如果是线性回归或随机森林)
    ax = axes[1, 1]
    if best_name in ['Linear Regression', 'Random Forest']:
        model = results[best_name]['model']
        if best_name == 'Linear Regression':
            importance = abs(model.coef_) / sum(abs(model.coef_))
        else:
            importance = model.feature_importances_
        
        feature_names = processor.best_features
        bars = ax.bar(feature_names, importance, color='lightblue')
        ax.set_ylabel('Feature Importance')
        ax.set_title(f'{best_name} Feature Importance')
        ax.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, imp in zip(bars, importance):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{imp:.3f}', ha='center', va='bottom', fontsize=8)
    else:
        # 所有模型R²对比
        model_names = list(results.keys())
        r2_scores = [results[name]['R2'] for name in model_names]
        
        colors = ['skyblue', 'lightgreen', 'lightcoral']
        bars = ax.bar(model_names, r2_scores, color=colors)
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax.set_ylabel('R² Score')
        ax.set_title('All Models Comparison')
        ax.tick_params(axis='x', rotation=45)
        
        for bar, score in zip(bars, r2_scores):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    # 保存
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, 'ahnu_3features_optimal_results.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"📊 最优结果图已保存: {save_path}")

def main():
    """主函数"""
    
    print("🚀 AHNU电池SOH预测最优解决方案 (3特征版本)")
    print("="*70)
    
    # 输出路径设置
    results_dir = r'E:\gitrepo\PINN4SOH\AHNU_Project\results'
    plots_dir = r'E:\gitrepo\PINN4SOH\AHNU_Project\plots'
    
    # 1. 数据处理
    processor = AHNUOptimalProcessor()
    X, y = processor.load_and_analyze_data()
    X_train, X_test, y_train, y_test = processor.prepare_data_for_training(X, y)
    
    # 2. 模型训练
    model = AHNUOptimalModel()
    results = model.train_all_models(X_train, y_train, X_test, y_test)
    
    # 3. 特征重要性分析
    importance = model.get_feature_importance(processor)
    
    # 4. 结果可视化
    plot_optimal_results(results, y_test, processor, plots_dir)
    
    # 5. 保存最佳模型和结果
    os.makedirs(results_dir, exist_ok=True)
    
    best_model_path = os.path.join(results_dir, 'best_ahnu_3features_model.pkl')
    scaler_path = os.path.join(results_dir, 'feature_scaler_3features.pkl')
    
    joblib.dump(model.best_model, best_model_path)
    joblib.dump(processor.scaler, scaler_path)
    
    # 保存预测结果
    best_pred = results[model.best_model_name]['pred']
    np.save(os.path.join(results_dir, 'optimal_true_labels_3features.npy'), y_test)
    np.save(os.path.join(results_dir, 'optimal_predictions_3features.npy'), best_pred)
    
    print(f"\n📁 模型和结果已保存到: {results_dir}")
    print(f"📊 图表已保存到: {plots_dir}")
    
    # 6. 总结报告
    print(f"\n📋 AHNU最优解决方案总结 (3特征版本)")
    print("="*70)
    
    best_metrics = results[model.best_model_name]
    print(f"🏆 最佳模型: {model.best_model_name}")
    print(f"📊 特征数量: {len(processor.best_features)} 个")
    print(f"📊 性能指标:")
    print(f"  - MSE: {best_metrics['MSE']:.8f}")
    print(f"  - MAE: {best_metrics['MAE']:.6f}")
    print(f"  - R²:  {best_metrics['R2']:.6f}")
    
    print(f"\n🎯 使用的3个特征:")
    for i, feature in enumerate(processor.best_features, 1):
        print(f"  {i}. {feature}")
    
    print(f"\n✨ 答案: 当前使用 **{len(processor.best_features)} 个特征** 预测SOH")
    
    return results

if __name__ == "__main__":
    results = main()