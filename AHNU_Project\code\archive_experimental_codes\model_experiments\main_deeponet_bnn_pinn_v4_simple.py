import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import argparse
import os
import matplotlib.pyplot as plt

# Check for GPU availability
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 0. AHNU Data Loader (复用v3，但简化特征)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class AHNUDataProcessorSimple:
    """简化的AHNU数据处理器 - 提取关键特征"""
    
    def __init__(self, file_path, args):
        self.file_path = file_path
        self.args = args
        self.scaler = StandardScaler()
        
    def load_and_process_data(self):
        """加载AHNU Excel数据并处理"""
        print(f"📊 加载AHNU数据: {self.file_path}")
        
        df = pd.read_excel(self.file_path)
        print(f"原始数据形状: {df.shape}")
        
        # 选择最相关的特征（基于诊断结果）
        features = self._extract_key_features(df)
        soh_labels = self._calculate_soh(df)
        
        # 创建数据对
        X1, X2, Y1, Y2 = self._create_sequence_pairs(features, soh_labels)
        
        return X1, X2, Y1, Y2
    
    def _extract_key_features(self, df):
        """提取关键特征（从17维简化到8维）"""
        features = []
        
        for _, row in df.iterrows():
            row_features = []
            
            # 1. 放电比容量 (与SOH最相关)
            discharge_cap = row['放电比容量(mAh/g)']
            row_features.append(discharge_cap)
            
            # 2. 充电比容量
            charge_cap = row['充电比容量(mAh/g)']
            row_features.append(charge_cap)
            
            # 3. 充放电效率
            efficiency = row['充放电效率(%)']
            row_features.append(efficiency)
            
            # 4. 循环号 (时间信息)
            cycle = row['循环号']
            row_features.append(cycle)
            
            # 5. 容量比率
            capacity_ratio = discharge_cap / charge_cap if charge_cap > 0 else 0
            row_features.append(capacity_ratio)
            
            # 6. 标准化循环号
            normalized_cycle = cycle / 216.0
            row_features.append(normalized_cycle)
            
            # 7. 容量衰减指标
            initial_capacity = df['放电比容量(mAh/g)'].iloc[0]
            capacity_retention = discharge_cap / initial_capacity
            row_features.append(capacity_retention)
            
            # 8. 效率-容量复合指标
            health_indicator = (discharge_cap * efficiency / 100.0) / initial_capacity
            row_features.append(health_indicator)
            
            features.append(row_features)
        
        features_array = np.array(features)
        print(f"提取的关键特征形状: {features_array.shape}")
        
        # 标准化特征
        if self.args.normalization_method == 'z-score':
            features_array = self.scaler.fit_transform(features_array)
        
        return features_array
    
    def _calculate_soh(self, df):
        """计算SOH值"""
        discharge_capacity = df['放电比容量(mAh/g)'].values
        initial_capacity = discharge_capacity[0]
        soh_values = discharge_capacity / initial_capacity
        
        # 限制在合理范围
        soh_values = np.clip(soh_values, 0.1, 1.2)
        
        print(f"SOH值范围: [{soh_values.min():.4f}, {soh_values.max():.4f}]")
        return soh_values.reshape(-1, 1)
    
    def _create_sequence_pairs(self, features, soh_labels):
        """创建序列对"""
        n_samples = len(features)
        
        X1, X2, Y1, Y2 = [], [], [], []
        
        for i in range(n_samples - 1):
            X1.append(features[i])
            X2.append(features[i + 1])
            Y1.append(soh_labels[i])
            Y2.append(soh_labels[i + 1])
        
        return np.array(X1), np.array(X2), np.array(Y1), np.array(Y2)

class AHNUDataLoaderSimple:
    """简化的AHNU数据加载器"""
    
    def __init__(self, file_path, args):
        self.file_path = file_path
        self.args = args
        self.processor = AHNUDataProcessorSimple(file_path, args)
    
    def get_dataloaders(self):
        """获取数据加载器"""
        X1, X2, Y1, Y2 = self.processor.load_and_process_data()
        
        tensor_X1 = torch.from_numpy(X1).float()
        tensor_X2 = torch.from_numpy(X2).float()
        tensor_Y1 = torch.from_numpy(Y1).float()
        tensor_Y2 = torch.from_numpy(Y2).float()
        
        print(f"张量形状: X1={tensor_X1.shape}, Y1={tensor_Y1.shape}")
        
        # 80/20划分
        split_idx = int(len(tensor_X1) * 0.8)
        
        train_X1, test_X1 = tensor_X1[:split_idx], tensor_X1[split_idx:]
        train_X2, test_X2 = tensor_X2[:split_idx], tensor_X2[split_idx:]
        train_Y1, test_Y1 = tensor_Y1[:split_idx], tensor_Y1[split_idx:]
        train_Y2, test_Y2 = tensor_Y2[:split_idx], tensor_Y2[split_idx:]
        
        # 验证集从训练集划分
        train_X1, valid_X1, train_X2, valid_X2, train_Y1, valid_Y1, train_Y2, valid_Y2 = \
            train_test_split(train_X1, train_X2, train_Y1, train_Y2,
                           test_size=0.2, random_state=42)
        
        train_loader = DataLoader(
            TensorDataset(train_X1, train_X2, train_Y1, train_Y2),
            batch_size=self.args.batch_size, shuffle=True
        )
        
        valid_loader = DataLoader(
            TensorDataset(valid_X1, valid_X2, valid_Y1, valid_Y2),
            batch_size=self.args.batch_size, shuffle=False
        )
        
        test_loader = DataLoader(
            TensorDataset(test_X1, test_X2, test_Y1, test_Y2),
            batch_size=self.args.batch_size, shuffle=False
        )
        
        print(f"数据集大小: 训练={len(train_loader.dataset)}, "
              f"验证={len(valid_loader.dataset)}, 测试={len(test_loader.dataset)}")
        
        return {'train': train_loader, 'valid': valid_loader, 'test': test_loader}

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 1. 简化的神经网络架构
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class SimpleSOHPredictor(nn.Module):
    """简化的SOH预测网络"""
    
    def __init__(self, input_dim=8, hidden_dims=[32, 16], dropout_rate=0.3):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        layers.append(nn.Sigmoid())  # 确保输出在[0,1]范围
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, x):
        return self.network(x)

class SimpleDynamicsNet(nn.Module):
    """简化的动力学网络"""
    
    def __init__(self, input_dim=10, hidden_dims=[24, 12], dropout_rate=0.2):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, 1))
        self.network = nn.Sequential(*layers)
        
    def forward(self, x):
        return self.network(x)

class AHNUSimplePINN(nn.Module):
    """简化的AHNU PINN模型"""
    
    def __init__(self, args):
        super().__init__()
        self.args = args
        
        # 简化的SOH预测网络 (8维输入)
        self.soh_predictor = SimpleSOHPredictor(
            input_dim=8,
            hidden_dims=[32, 16],
            dropout_rate=0.3
        ).to(device)
        
        # 简化的动力学网络 (8+1+1=10维输入: 特征+SOH+时间)
        self.dynamics_net = SimpleDynamicsNet(
            input_dim=10,
            hidden_dims=[24, 12],
            dropout_rate=0.2
        ).to(device)
        
        # 损失函数
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()
        
        # 优化器
        self.optimizer = torch.optim.Adam(
            self.parameters(),
            lr=args.lr,
            weight_decay=args.weight_decay
        )
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.8, patience=10, verbose=True
        )
        
        self.training_history = {
            'train_loss': [], 'val_loss': [], 'physics_loss': []
        }
    
    def forward(self, x):
        """前向传播"""
        # SOH预测
        soh = self.soh_predictor(x)
        
        # 构建动力学网络输入
        batch_size = x.size(0)
        time = torch.linspace(0, 1, batch_size).unsqueeze(1).to(device)
        
        dynamics_input = torch.cat([x, soh, time], dim=1)
        
        # 动力学预测
        dynamics = self.dynamics_net(dynamics_input)
        
        return soh, dynamics
    
    def compute_physics_loss(self, soh1, soh2, y1, y2):
        """计算物理约束损失"""
        # 单调递减约束 (SOH应该递减)
        monotonic_loss = torch.relu(soh2 - soh1).mean()
        
        # 平滑性约束
        pred_diff = torch.abs(soh2 - soh1)
        target_diff = torch.abs(y2 - y1)
        smoothness_loss = torch.abs(pred_diff - target_diff).mean()
        
        return monotonic_loss + 0.1 * smoothness_loss
    
    def train_one_epoch(self, train_loader, val_loader):
        """训练一轮"""
        self.train()
        train_losses = []
        
        for x1, x2, y1, y2 in train_loader:
            x1, x2, y1, y2 = [d.to(device) for d in [x1, x2, y1, y2]]
            
            # 前向传播
            soh1, dyn1 = self.forward(x1)
            soh2, dyn2 = self.forward(x2)
            
            # 数据损失
            data_loss = 0.5 * (self.mse_loss(soh1, y1) + self.mse_loss(soh2, y2))
            
            # 物理约束损失
            physics_loss = self.compute_physics_loss(soh1, soh2, y1, y2)
            
            # PDE损失 (简化)
            pde_loss = 0.5 * (self.mse_loss(dyn1, torch.zeros_like(dyn1)) +
                             self.mse_loss(dyn2, torch.zeros_like(dyn2)))
            
            # 总损失
            total_loss = (data_loss + 
                         self.args.alpha * pde_loss +
                         self.args.beta * physics_loss)
            
            # 反向传播
            self.optimizer.zero_grad()
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            train_losses.append(total_loss.item())
        
        # 验证
        val_loss = self.validate(val_loader)
        
        # 更新学习率
        self.scheduler.step(val_loss)
        
        avg_train_loss = np.mean(train_losses)
        
        # 记录历史
        self.training_history['train_loss'].append(avg_train_loss)
        self.training_history['val_loss'].append(val_loss)
        
        return avg_train_loss, val_loss
    
    def validate(self, val_loader):
        """验证"""
        self.eval()
        val_losses = []
        
        with torch.no_grad():
            for x1, _, y1, _ in val_loader:
                x1, y1 = x1.to(device), y1.to(device)
                
                soh1, _ = self.forward(x1)
                loss = self.mse_loss(soh1, y1)
                val_losses.append(loss.item())
        
        return np.mean(val_losses)
    
    def predict(self, dataloader):
        """预测"""
        self.eval()
        all_preds, all_true = [], []
        
        with torch.no_grad():
            for x1, _, y1, _ in dataloader:
                x1, y1 = x1.to(device), y1.to(device)
                
                soh1, _ = self.forward(x1)
                
                all_preds.append(soh1.cpu().numpy())
                all_true.append(y1.cpu().numpy())
        
        return np.concatenate(all_true), np.concatenate(all_preds)

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 2. 训练和评估
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def plot_simple_results(true, pred, save_path):
    """绘制简化结果"""
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 预测对比
    ax = axes[0]
    cycles = range(len(true))
    ax.plot(cycles, true, label='True SOH', color='blue', linewidth=2)
    ax.plot(cycles, pred, label='Predicted SOH', color='red', linewidth=2)
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('SOH')
    ax.set_title('SOH Prediction Comparison')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 误差分析
    ax = axes[1]
    errors = np.abs(pred.flatten() - true.flatten())
    ax.plot(cycles, errors, color='green', linewidth=1)
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('Absolute Error')
    ax.set_title(f'Prediction Error (MAE: {np.mean(errors):.6f})')
    ax.grid(True, alpha=0.3)
    
    # 散点图
    ax = axes[2]
    ax.scatter(true.flatten(), pred.flatten(), alpha=0.7, s=20)
    min_val, max_val = min(true.min(), pred.min()), max(true.max(), pred.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.set_title('True vs Predicted')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Results saved: {save_path}")

def get_simple_args():
    """简化的参数"""
    parser = argparse.ArgumentParser(description="Simple AHNU SOH Prediction")
    
    parser.add_argument('--data_file', type=str,
                       default=r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>')
    parser.add_argument('--epochs', type=int, default=100)
    parser.add_argument('--batch_size', type=int, default=16)  # 更小的batch
    parser.add_argument('--normalization_method', type=str, default='z-score')
    
    parser.add_argument('--lr', type=float, default=0.001)
    parser.add_argument('--weight_decay', type=float, default=0.01)  # 更强的正则化
    parser.add_argument('--alpha', type=float, default=0.1)  # 降低PDE权重
    parser.add_argument('--beta', type=float, default=0.5)   # 降低物理权重
    
    return parser.parse_args()

if __name__ == "__main__":
    print("🚀 启动简化AHNU SOH预测模型 v4")
    args = get_simple_args()
    
    print(f"设备: {device}")
    
    # 创建目录
    results_dir = './neural_operator_experiment/results_v4_simple'
    plots_dir = './neural_operator_experiment/plots_v4_simple'
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(plots_dir, exist_ok=True)
    
    print(f"\n📊 简化模型配置:")
    print(f"  - 特征维度: 8维 (简化)")
    print(f"  - 网络结构: 简化MLP")
    print(f"  - 批次大小: {args.batch_size}")
    print(f"  - 学习率: {args.lr}")
    print(f"  - 权重衰减: {args.weight_decay}")
    
    # 加载数据
    data_loader = AHNUDataLoaderSimple(args.data_file, args)
    loaders = data_loader.get_dataloaders()
    
    # 创建模型
    model = AHNUSimplePINN(args)
    model_save_path = os.path.join(results_dir, 'simple_ahnu_model.pth')
    
    print(f"\n🏋️  开始训练...")
    
    best_val_loss = float('inf')
    patience = 0
    patience_limit = 20
    
    for epoch in range(1, args.epochs + 1):
        train_loss, val_loss = model.train_one_epoch(loaders['train'], loaders['valid'])
        
        # 早停
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience = 0
            torch.save(model.state_dict(), model_save_path)
        else:
            patience += 1
        
        if epoch % 10 == 0:
            print(f"  轮次 {epoch:3d}: 训练损失={train_loss:.6f}, 验证损失={val_loss:.6f}")
        
        if patience >= patience_limit:
            print(f"  早停于第 {epoch} 轮")
            break
    
    # 测试
    print(f"\n🧪 测试中...")
    model.load_state_dict(torch.load(model_save_path))
    true_labels, predictions = model.predict(loaders['test'])
    
    # 计算指标
    mse = np.mean((predictions.flatten() - true_labels.flatten()) ** 2)
    mae = np.mean(np.abs(predictions.flatten() - true_labels.flatten()))
    r2 = 1 - np.sum((true_labels.flatten() - predictions.flatten()) ** 2) / np.sum(
        (true_labels.flatten() - np.mean(true_labels)) ** 2)
    
    print(f"\n📈 简化模型结果:")
    print(f"  - MSE: {mse:.8f}")
    print(f"  - MAE: {mae:.8f}")
    print(f"  - R²:  {r2:.6f}")
    
    # 保存结果
    np.save(os.path.join(results_dir, "simple_true_labels.npy"), true_labels)
    np.save(os.path.join(results_dir, "simple_predictions.npy"), predictions)
    
    # 绘图
    plot_save_path = os.path.join(plots_dir, 'simple_soh_results.png')
    plot_simple_results(true_labels, predictions, plot_save_path)
    
    print(f"\n🎉 简化模型训练完成!")
    print(f"📁 结果保存在: {results_dir}")
    print(f"📊 图表保存在: {plots_dir}")
    
    # 分析改进效果
    if r2 > 0:
        print(f"\n✅ R² > 0, 模型有效!")
    else:
        print(f"\n⚠️  R² < 0, 仍需进一步优化")
    
    # 估算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    print(f"📊 模型参数数量: {total_params:,}")
    print(f"📊 参数/样本比: {total_params/len(loaders['train'].dataset):.1f}")