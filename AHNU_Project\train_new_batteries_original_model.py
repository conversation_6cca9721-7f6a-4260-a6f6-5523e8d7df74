"""
使用原始Model训练新电池数据
Train new battery data using original Model
4个电池作为训练集，1个电池作为测试集

作者：AI Assistant
日期：2025-01-27
"""
import sys
import os
import torch
import numpy as np
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import scienceplots

# 添加路径以导入原始Model
sys.path.append('../')
from Model.Model import PINN, Solution_u, MLP
from utils.util import AverageMeter, get_logger, eval_metrix

# 复制原始dataloader的DF类
class DF():
    def __init__(self, args):
        self.normalization = True
        self.normalization_method = args.normalization_method
        self.args = args

    def _3_sigma(self, Ser1):
        rule = (Ser1.mean() - 3 * Ser1.std() > Ser1) | (Ser1.mean() + 3 * Ser1.std() < Ser1)
        index = np.arange(Ser1.shape[0])[rule]
        return index

    def delete_3_sigma(self, df):
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.dropna()
        df = df.reset_index(drop=True)
        out_index = []
        for col in df.columns:
            index = self._3_sigma(df[col])
            out_index.extend(index)
        out_index = list(set(out_index))
        df = df.drop(out_index, axis=0)
        df = df.reset_index(drop=True)
        return df

    def read_one_csv(self, file_name, nominal_capacity=None):
        """读取CSV文件并按照原始Model的方式处理"""
        df = pd.read_csv(file_name)
        
        # 关键步骤：在倒数第二列插入cycle index（与原始Model完全一致）
        df.insert(df.shape[1]-1, 'cycle index', np.arange(df.shape[0]))
        
        df = self.delete_3_sigma(df)

        if nominal_capacity is not None:
            # 计算SOH：capacity/nominal_capacity（与原始Model一致）
            df['capacity'] = df['capacity'] / nominal_capacity
            
            # 归一化特征（除了最后一列capacity）
            f_df = df.iloc[:, :-1]
            if self.normalization_method == 'min-max':
                f_df = 2 * (f_df - f_df.min()) / (f_df.max() - f_df.min()) - 1
            elif self.normalization_method == 'z-score':
                f_df = (f_df - f_df.mean()) / f_df.std()

            df.iloc[:, :-1] = f_df

        return df

    def load_one_battery(self, path, nominal_capacity=None):
        df = self.read_one_csv(path, nominal_capacity)
        x = df.iloc[:, :-1].values  # 16特征 + 1cycle_index = 17维
        y = df.iloc[:, -1].values   # capacity (SOH)
        x1 = x[:-1]
        x2 = x[1:]
        y1 = y[:-1]
        y2 = y[1:]
        return (x1, y1), (x2, y2)

    def load_multiple_batteries(self, path_list, nominal_capacity_list):
        """加载多个电池的数据"""
        X1, X2, Y1, Y2 = [], [], [], []
        
        for path, nominal_capacity in zip(path_list, nominal_capacity_list):
            print(f"   - 加载: {os.path.basename(path)} (nominal_capacity: {nominal_capacity:.2f})")
            (x1, y1), (x2, y2) = self.load_one_battery(path, nominal_capacity)
            X1.append(x1)
            X2.append(x2)
            Y1.append(y1)
            Y2.append(y2)

        X1 = np.concatenate(X1, axis=0)
        X2 = np.concatenate(X2, axis=0)
        Y1 = np.concatenate(Y1, axis=0)
        Y2 = np.concatenate(Y2, axis=0)

        tensor_X1 = torch.from_numpy(X1).float()
        tensor_X2 = torch.from_numpy(X2).float()
        tensor_Y1 = torch.from_numpy(Y1).float().view(-1, 1)
        tensor_Y2 = torch.from_numpy(Y2).float().view(-1, 1)

        return tensor_X1, tensor_X2, tensor_Y1, tensor_Y2

class NewBatteryData(DF):
    def __init__(self, args):
        super(NewBatteryData, self).__init__(args)
        self.args = args
        
        # 电池文件和对应的nominal capacity
        self.battery_info = {
            'Si@C-1': {'file': './data/<EMAIL>', 'nominal_capacity': 1.1141},
            'Si@C-2': {'file': './data/<EMAIL>', 'nominal_capacity': 1.0000},
            'Si@C-3': {'file': './data/<EMAIL>', 'nominal_capacity': 1.0016},
            'Si@C-4': {'file': './data/<EMAIL>', 'nominal_capacity': 1.0000},
            'Si@C-5': {'file': './data/<EMAIL>', 'nominal_capacity': 1.0000}
        }

    def load_train_test_data(self, train_batteries, test_batteries):
        """加载训练和测试数据"""
        print(f"📊 数据加载配置:")
        print(f"   - 训练电池: {train_batteries}")
        print(f"   - 测试电池: {test_batteries}")
        
        # 准备训练数据
        train_paths = []
        train_capacities = []
        for battery in train_batteries:
            if battery in self.battery_info:
                train_paths.append(self.battery_info[battery]['file'])
                train_capacities.append(self.battery_info[battery]['nominal_capacity'])
        
        print(f"\n🏋️ 加载训练数据:")
        train_X1, train_X2, train_Y1, train_Y2 = self.load_multiple_batteries(train_paths, train_capacities)
        
        # 从训练数据中分出验证集
        train_X1, valid_X1, train_X2, valid_X2, train_Y1, valid_Y1, train_Y2, valid_Y2 = \
            train_test_split(train_X1, train_X2, train_Y1, train_Y2, test_size=0.2, random_state=420)
        
        # 准备测试数据
        test_paths = []
        test_capacities = []
        for battery in test_batteries:
            if battery in self.battery_info:
                test_paths.append(self.battery_info[battery]['file'])
                test_capacities.append(self.battery_info[battery]['nominal_capacity'])
        
        print(f"\n🧪 加载测试数据:")
        test_X1, test_X2, test_Y1, test_Y2 = self.load_multiple_batteries(test_paths, test_capacities)
        
        # 创建DataLoader
        train_loader = DataLoader(TensorDataset(train_X1, train_X2, train_Y1, train_Y2),
                                  batch_size=self.args.batch_size, shuffle=True)
        valid_loader = DataLoader(TensorDataset(valid_X1, valid_X2, valid_Y1, valid_Y2),
                                  batch_size=self.args.batch_size, shuffle=True)
        test_loader = DataLoader(TensorDataset(test_X1, test_X2, test_Y1, test_Y2),
                                 batch_size=self.args.batch_size, shuffle=False)

        print(f"\n📊 数据统计:")
        print(f"   - 训练样本: {len(train_X1)}")
        print(f"   - 验证样本: {len(valid_X1)}")
        print(f"   - 测试样本: {len(test_X1)}")
        print(f"   - 训练批次: {len(train_loader)}")
        print(f"   - 验证批次: {len(valid_loader)}")
        print(f"   - 测试批次: {len(test_loader)}")

        return {'train': train_loader, 'valid': valid_loader, 'test': test_loader}

class MockArgs:
    """模拟参数类，与原始Model保持一致"""
    def __init__(self):
        # 数据参数
        self.batch_size = 64
        self.normalization_method = 'z-score'
        
        # 模型参数
        self.F_layers_num = 3
        self.F_hidden_dim = 60
        
        # 训练参数
        self.epochs = 150
        self.lr = 1e-3
        self.warmup_epochs = 10
        self.warmup_lr = 5e-4
        self.final_lr = 1e-4
        self.lr_F = 1e-3
        self.iter_per_epoch = 1
        
        # 损失权重
        self.alpha = 1.0  # PDE loss weight
        self.beta = 1.0   # Physics loss weight
        
        # 日志和保存
        self.save_folder = './results_new_batteries_original'
        self.log_dir = 'training.log'
        self.early_stop = 25

def create_results_visualization(results_dir):
    """创建结果可视化"""
    print(f"\n📊 生成结果可视化...")
    
    # 检查结果文件
    true_file = os.path.join(results_dir, 'true_label.npy')
    pred_file = os.path.join(results_dir, 'pred_label.npy')
    
    if not os.path.exists(true_file) or not os.path.exists(pred_file):
        print("⚠️  结果文件不存在，跳过可视化")
        return
    
    # 加载结果
    true_labels = np.load(true_file)
    pred_labels = np.load(pred_file)
    
    # 计算性能指标
    mse = np.mean((pred_labels.flatten() - true_labels.flatten()) ** 2)
    mae = np.mean(np.abs(pred_labels.flatten() - true_labels.flatten()))
    rmse = np.sqrt(mse)
    r2 = 1 - np.sum((true_labels.flatten() - pred_labels.flatten()) ** 2) / np.sum((true_labels.flatten() - np.mean(true_labels)) ** 2)
    
    print(f"📈 性能指标:")
    print(f"   - MSE: {mse:.8f}")
    print(f"   - MAE: {mae:.8f}")
    print(f"   - RMSE: {rmse:.8f}")
    print(f"   - R²: {r2:.6f}")
    
    # 创建可视化
    plt.style.use(['science', 'nature'])
    fig, axes = plt.subplots(2, 2, figsize=(12, 8), dpi=200)
    fig.suptitle('New Battery Data - Original Model Results', fontsize=16)
    
    # 主预测图
    ax = axes[0, 0]
    ax.plot(true_labels, label='Ground Truth', color='#403990', linewidth=2, zorder=3)
    ax.plot(pred_labels, label='Original Model Prediction', color='#CF3D3E', linewidth=2, zorder=2)
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('SOH')
    ax.set_title(f'SOH Prediction Results')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 误差分析
    ax = axes[0, 1]
    errors = np.abs(pred_labels.flatten() - true_labels.flatten())
    ax.plot(errors, color='#2E8B57', linewidth=1)
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('Absolute Error')
    ax.set_title(f'Prediction Error (MAE: {mae:.6f})')
    ax.grid(True, alpha=0.3)
    
    # 散点图
    ax = axes[1, 0]
    ax.scatter(true_labels.flatten(), pred_labels.flatten(), alpha=0.6, s=10, color='#4169E1')
    min_val = min(true_labels.min(), pred_labels.min())
    max_val = max(true_labels.max(), pred_labels.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.set_title(f'True vs Predicted (R² = {r2:.3f})')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 误差分布
    ax = axes[1, 1]
    ax.hist(errors, bins=20, alpha=0.7, color='#FF6347', edgecolor='black')
    ax.set_xlabel('Absolute Error')
    ax.set_ylabel('Frequency')
    ax.set_title('Error Distribution')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    plot_dir = './plots_new_batteries_original'
    os.makedirs(plot_dir, exist_ok=True)
    plot_file = os.path.join(plot_dir, 'training_results.png')
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"   - 图表已保存: {plot_file}")
    
    return {
        'mse': mse,
        'mae': mae,
        'rmse': rmse,
        'r2': r2,
        'true_labels': true_labels,
        'pred_labels': pred_labels
    }

def main():
    print("🚀 使用原始Model训练新电池数据")
    print("=" * 60)
    
    # 创建参数
    args = MockArgs()
    
    # 创建保存目录
    os.makedirs(args.save_folder, exist_ok=True)
    
    print(f"📊 训练配置:")
    print(f"   - 批次大小: {args.batch_size}")
    print(f"   - 训练轮数: {args.epochs}")
    print(f"   - 学习率: {args.lr}")
    print(f"   - PDE损失权重: {args.alpha}")
    print(f"   - 物理损失权重: {args.beta}")
    print(f"   - 结果保存: {args.save_folder}")
    
    # 定义训练和测试电池
    train_batteries = ['Si@C-1', 'Si@C-2', 'Si@C-3', 'Si@C-4']  # 4个电池作为训练集
    test_batteries = ['Si@C-5']  # 1个电池作为测试集
    
    # 加载数据
    data_loader = NewBatteryData(args)
    loaders = data_loader.load_train_test_data(train_batteries, test_batteries)
    
    # 创建模型
    model = PINN(args)
    
    print(f"\n🏗️ 模型架构:")
    print(f"   - Solution_u: 17 → 32 → 1")
    print(f"   - dynamical_F: 35 → {args.F_hidden_dim} → 1")
    
    # 开始训练
    print(f"\n🏋️ 开始训练...")
    model.Train(trainloader=loaders['train'], validloader=loaders['valid'], testloader=loaders['test'])
    
    print(f"✅ 训练完成!")
    
    # 创建结果可视化
    results = create_results_visualization(args.save_folder)
    
    if results:
        print(f"\n🎉 训练总结:")
        print(f"   - 训练电池: {', '.join(train_batteries)}")
        print(f"   - 测试电池: {', '.join(test_batteries)}")
        print(f"   - 最终MSE: {results['mse']:.8f}")
        print(f"   - 最终MAE: {results['mae']:.8f}")
        print(f"   - 最终R²: {results['r2']:.6f}")
        print(f"   - 结果保存在: {args.save_folder}")

if __name__ == "__main__":
    main()
