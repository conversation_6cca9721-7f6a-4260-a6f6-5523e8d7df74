#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

def analyze_ahnu_data():
    """分析AHNU数据集结构"""
    
    # 数据文件路径
    data_path = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
    
    print("="*60)
    print("AHNU数据集分析")
    print("="*60)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(data_path)
        
        print(f"✅ 成功读取文件: {data_path}")
        print(f"📊 数据形状: {df.shape}")
        print(f"📋 列数: {df.shape[1]}, 行数: {df.shape[0]}")
        
        print("\n📝 列名信息:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1:2d}. {col}")
        
        print(f"\n🔍 数据类型:")
        for col, dtype in df.dtypes.items():
            print(f"  {col}: {dtype}")
        
        print(f"\n📈 前10行数据预览:")
        print(df.head(10))
        
        print(f"\n📊 基本统计信息:")
        print(df.describe())
        
        # 检查是否有缺失值
        print(f"\n❌ 缺失值统计:")
        missing_data = df.isnull().sum()
        for col, missing_count in missing_data.items():
            if missing_count > 0:
                print(f"  {col}: {missing_count} ({missing_count/len(df)*100:.2f}%)")
        
        if missing_data.sum() == 0:
            print("  ✅ 没有缺失值")
        
        # 分析可能的电压电流列
        print(f"\n🔋 可能的电压电流相关列:")
        voltage_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in ['voltage', 'volt', 'v', '电压'])]
        current_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in ['current', 'curr', 'i', 'a', '电流'])]
        
        print(f"  可能的电压列: {voltage_cols}")
        print(f"  可能的电流列: {current_cols}")
        
        # 分析数值型列
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        print(f"\n🔢 数值型列 ({len(numeric_cols)}列):")
        for i, col in enumerate(numeric_cols):
            print(f"  {i+1:2d}. {col}")
        
        # 如果有循环相关的信息
        if any('cycle' in col.lower() for col in df.columns):
            cycle_cols = [col for col in df.columns if 'cycle' in col.lower()]
            print(f"\n🔄 循环相关列: {cycle_cols}")
            
            for col in cycle_cols:
                if col in df.columns:
                    print(f"  {col} 范围: {df[col].min()} - {df[col].max()}")
        
        # 保存数据概览到文件
        output_path = r'E:\gitrepo\PINN4SOH\ahnu_data_analysis.txt'
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("AHNU数据集分析报告\n")
            f.write("="*50 + "\n")
            f.write(f"数据形状: {df.shape}\n")
            f.write(f"列名: {df.columns.tolist()}\n")
            f.write(f"数据类型:\n{df.dtypes}\n")
            f.write(f"基本统计:\n{df.describe()}\n")
        
        print(f"\n💾 分析报告已保存到: {output_path}")
        
        return df
        
    except Exception as e:
        print(f"❌ 读取文件时出错: {str(e)}")
        return None

def extract_battery_features(df):
    """从数据中提取17维电池特征"""
    
    if df is None:
        return None
    
    print("\n" + "="*60)
    print("电池特征提取分析")
    print("="*60)
    
    # 分析可能用于特征提取的列
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    print(f"🔢 可用于特征提取的数值列 ({len(numeric_cols)}列):")
    for i, col in enumerate(numeric_cols):
        print(f"  {i+1:2d}. {col} - 范围: [{df[col].min():.6f}, {df[col].max():.6f}]")
    
    # 如果列数足够，尝试提取统计特征
    if len(numeric_cols) >= 2:
        print(f"\n📊 建议的17维特征提取策略:")
        print(f"  - 可以从 {len(numeric_cols)} 个数值列中计算统计特征")
        print(f"  - 每列可计算: 均值、标准差、最小值、最大值、中位数等")
        
        # 示例：如果有电压和电流数据
        sample_features = []
        feature_names = []
        
        for col in numeric_cols[:3]:  # 取前几列作为示例
            values = df[col].dropna()
            
            # 计算统计特征
            mean_val = values.mean()
            std_val = values.std()
            min_val = values.min()
            max_val = values.max()
            median_val = values.median()
            
            sample_features.extend([mean_val, std_val, min_val, max_val, median_val])
            feature_names.extend([
                f"{col}_mean", f"{col}_std", f"{col}_min", f"{col}_max", f"{col}_median"
            ])
        
        print(f"\n🎯 示例特征计算 (前{len(sample_features)}维):")
        for i, (name, val) in enumerate(zip(feature_names, sample_features)):
            print(f"  {i+1:2d}. {name}: {val:.6f}")
        
        return sample_features, feature_names
    
    return None, None

def analyze_soh_calculation(df):
    """分析SOH值计算方法"""
    
    if df is None:
        return None
    
    print("\n" + "="*60)
    print("SOH值计算分析")
    print("="*60)
    
    # 查找可能的容量相关列
    capacity_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in 
                    ['capacity', 'cap', 'soh', 'health', '容量', '健康'])]
    
    print(f"🔋 可能的容量/SOH相关列:")
    for col in capacity_cols:
        print(f"  - {col}: 范围 [{df[col].min():.6f}, {df[col].max():.6f}]")
    
    # 如果没有直接的SOH列，建议计算方法
    if not capacity_cols:
        print(f"⚠️  未找到明显的SOH/容量列")
        print(f"💡 SOH计算建议:")
        print(f"  - SOH = 当前容量 / 额定容量")
        print(f"  - 需要确定额定容量值")
        print(f"  - 或者基于充电数据计算容量")
    
    # 分析是否有循环数据用于SOH退化建模
    if any('cycle' in col.lower() for col in df.columns):
        cycle_cols = [col for col in df.columns if 'cycle' in col.lower()]
        print(f"🔄 发现循环数据，可用于SOH退化建模:")
        for col in cycle_cols:
            print(f"  - {col}: {df[col].nunique()} 个不同值")
    
    return capacity_cols

if __name__ == "__main__":
    # 分析数据
    df = analyze_ahnu_data()
    
    if df is not None:
        # 特征提取分析
        features, feature_names = extract_battery_features(df)
        
        # SOH计算分析
        soh_cols = analyze_soh_calculation(df)
        
        print(f"\n🎯 总结:")
        print(f"  - 数据形状: {df.shape}")
        print(f"  - 数值列数: {len(df.select_dtypes(include=[np.number]).columns)}")
        print(f"  - 可提取特征维度: 根据列数可灵活调整到17维")
        print(f"  - SOH计算: 需要进一步确定容量数据或计算方法")