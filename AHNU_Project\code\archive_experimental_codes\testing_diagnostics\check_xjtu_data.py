import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from dataloader.dataloader import XJTUdata
import argparse

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--batch_size', type=int, default=32)
    parser.add_argument('--normalization_method', type=str, default='z-score')
    parser.add_argument('--log_dir', type=str, default=None)
    parser.add_argument('--save_folder', type=str, default=None)
    return parser.parse_args()

if __name__ == "__main__":
    args = get_args()
    xjtu = XJTUdata(root='e:/gitrepo/PINN4SOH/data/XJTU data', args=args)
    loader_dict = xjtu.read_one_batch('2C')
    train_loader = loader_dict['train']
    
    for x1, x2, y1, y2 in train_loader:
        print('x1形状:', x1.shape)
        print('x2形状:', x2.shape)
        print('y1形状:', y1.shape)
        print('y2形状:', y2.shape)
        break