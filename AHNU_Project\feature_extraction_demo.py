#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XJTU电池数据特征提取演示脚本
展示如何从cycledata.csv提取17维特征用于PINN4SOH模型

特征说明：
1-8维：电压特征（均值、标准差、峰度、偏度、CC_Q、CC_time、斜率、熵）
9-16维：电流特征（均值、标准差、峰度、偏度、CV_Q、CV_time、斜率、熵）
17维：SOH标签（当前容量/初始容量）

数据格式：
- 输入：16维特征向量
- 输出：SOH值（0-1之间的浮点数）

作者：AI Assistant
日期：2025-01-27
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt

def load_and_preview_data():
    """加载并预览数据"""
    print("=== XJTU电池数据特征提取演示 ===\n")
    
    # 加载原始数据
    csv_path = "AHNU_Project/data/cycledata.csv"
    df = pd.read_csv(csv_path, encoding='utf-8')
    
    print(f"1. 原始数据概览:")
    print(f"   - 数据形状: {df.shape}")
    print(f"   - 列名: {list(df.columns)}")
    print(f"   - 循环数量: {len(df['循环号'].unique())}")
    print(f"   - 工步类型: {df['工步类型'].unique()}")
    
    # 显示前几行数据
    print(f"\n2. 数据样例:")
    print(df.head(3).to_string())
    
    return df

def analyze_cycle_structure(df):
    """分析循环结构"""
    print(f"\n3. 循环结构分析:")
    
    # 分析第一个循环
    cycle_1 = df[df['循环号'] == 1]
    print(f"   第1个循环:")
    print(f"   - 总数据点: {len(cycle_1)}")
    print(f"   - 工步分布: {cycle_1['工步类型'].value_counts().to_dict()}")
    print(f"   - 时间范围: {cycle_1['总时间(h)'].min():.3f} - {cycle_1['总时间(h)'].max():.3f} 小时")
    print(f"   - 电压范围: {cycle_1['电压(V)'].min():.3f} - {cycle_1['电压(V)'].max():.3f} V")
    print(f"   - 电流范围: {cycle_1['电流(mA)'].min():.3f} - {cycle_1['电流(mA)'].max():.3f} mA")
    
    # 计算容量
    discharge_data = cycle_1[cycle_1['工步类型'] == '恒流放电']
    capacity_1 = discharge_data['比容量(mAh/g)'].max()
    print(f"   - 第1循环容量: {capacity_1:.2f} mAh/g")

def demonstrate_feature_extraction():
    """演示特征提取过程"""
    print(f"\n4. 特征提取演示:")
    
    # 加载提取好的特征
    features_path = "AHNU_Project/data/AHNU_17_features.csv"
    df_features = pd.read_csv(features_path)
    
    print(f"   - 提取的特征数量: {len(df_features)} 个循环")
    print(f"   - 特征维度: {df_features.shape[1] - 2} 维 (除去cycle和SOH列)")
    print(f"   - SOH范围: {df_features['SOH'].min():.4f} - {df_features['SOH'].max():.4f}")
    
    # 显示特征名称
    feature_names = [col for col in df_features.columns if col not in ['cycle', 'SOH']]
    print(f"\n   特征列表:")
    for i, name in enumerate(feature_names, 1):
        print(f"   {i:2d}. {name}")
    
    # 显示前5个循环的特征
    print(f"\n   前5个循环的特征样例:")
    display_cols = ['cycle'] + feature_names[:4] + ['SOH']
    print(df_features[display_cols].head().to_string(index=False, float_format='%.4f'))
    
    return df_features

def analyze_soh_degradation(df_features):
    """分析SOH退化趋势"""
    print(f"\n5. SOH退化分析:")
    
    # 基本统计
    soh_stats = df_features['SOH'].describe()
    print(f"   SOH统计信息:")
    for stat, value in soh_stats.items():
        print(f"   - {stat}: {value:.4f}")
    
    # 退化趋势
    initial_soh = df_features['SOH'].iloc[0]
    final_soh = df_features['SOH'].iloc[-1]
    degradation = (initial_soh - final_soh) / initial_soh * 100
    
    print(f"\n   退化趋势:")
    print(f"   - 初始SOH: {initial_soh:.4f}")
    print(f"   - 最终SOH: {final_soh:.4f}")
    print(f"   - 总退化率: {degradation:.2f}%")
    print(f"   - 循环寿命: {len(df_features)} 个循环")

def create_visualization(df_features):
    """创建可视化图表"""
    print(f"\n6. 生成可视化图表...")
    
    # 设置字体为Arial
    plt.rcParams['font.family'] = 'Arial'
    plt.rcParams['font.size'] = 10
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('AHNU Battery Data Feature Analysis', fontsize=16, fontfamily='Arial')
    
    # SOH退化曲线
    axes[0, 0].plot(df_features['cycle'], df_features['SOH'], 'b-', linewidth=2)
    axes[0, 0].set_title('SOH Degradation Curve', fontfamily='Arial')
    axes[0, 0].set_xlabel('Cycle Number', fontfamily='Arial')
    axes[0, 0].set_ylabel('SOH', fontfamily='Arial')
    axes[0, 0].grid(False)  # 关闭网格
    
    # 电压均值变化
    axes[0, 1].plot(df_features['cycle'], df_features['voltage_mean'], 'r-', linewidth=2)
    axes[0, 1].set_title('Voltage Mean Evolution', fontfamily='Arial')
    axes[0, 1].set_xlabel('Cycle Number', fontfamily='Arial')
    axes[0, 1].set_ylabel('Voltage Mean (V)', fontfamily='Arial')
    axes[0, 1].grid(False)  # 关闭网格
    
    # 电流均值变化
    axes[1, 0].plot(df_features['cycle'], df_features['current_mean'], 'g-', linewidth=2)
    axes[1, 0].set_title('Current Mean Evolution', fontfamily='Arial')
    axes[1, 0].set_xlabel('Cycle Number', fontfamily='Arial')
    axes[1, 0].set_ylabel('Current Mean (A)', fontfamily='Arial')
    axes[1, 0].grid(False)  # 关闭网格
    
    # 特征相关性热图（选择几个主要特征）
    selected_features = ['voltage_mean', 'voltage_std', 'current_mean', 'current_std', 'SOH']
    corr_matrix = df_features[selected_features].corr()
    
    im = axes[1, 1].imshow(corr_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
    axes[1, 1].set_title('Feature Correlation Matrix', fontfamily='Arial')
    axes[1, 1].set_xticks(range(len(selected_features)))
    axes[1, 1].set_yticks(range(len(selected_features)))
    axes[1, 1].set_xticklabels(selected_features, rotation=45, fontfamily='Arial')
    axes[1, 1].set_yticklabels(selected_features, fontfamily='Arial')
    axes[1, 1].grid(False)  # 关闭网格
    
    # 添加相关系数文本
    for i in range(len(selected_features)):
        for j in range(len(selected_features)):
            text = axes[1, 1].text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',
                                 ha="center", va="center", color="black", fontsize=8, fontfamily='Arial')
    
    # 设置colorbar字体
    cbar = plt.colorbar(im, ax=axes[1, 1])
    cbar.ax.tick_params(labelsize=8)
    for label in cbar.ax.get_yticklabels():
        label.set_fontfamily('Arial')
    
    # 设置所有子图的刻度标签字体
    for ax in axes.flat:
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontfamily('Arial')
    
    plt.tight_layout()
    plt.savefig('AHNU_Project/data/AHNU_features_analysis.png', dpi=300, bbox_inches='tight')
    print(f"   图表已保存到: AHNU_Project/data/AHNU_features_analysis.png")

def generate_model_ready_data(df_features):
    """生成模型就绪的数据"""
    print(f"\n7. 生成模型训练数据:")
    
    # 分离特征和标签
    feature_cols = [col for col in df_features.columns if col not in ['cycle', 'SOH']]
    X = df_features[feature_cols].values  # 16维特征
    y = df_features['SOH'].values         # SOH标签
    
    print(f"   - 特征矩阵 X: {X.shape}")
    print(f"   - 标签向量 y: {y.shape}")
    print(f"   - 特征范围:")
    
    for i, col in enumerate(feature_cols):
        print(f"     {i+1:2d}. {col:15s}: [{X[:, i].min():8.4f}, {X[:, i].max():8.4f}]")
    
    # 保存为numpy格式
    np.save('AHNU_Project/data/AHNU_features_X.npy', X)
    np.save('AHNU_Project/data/AHNU_features_y.npy', y)
    
    print(f"\n   数据已保存:")
    print(f"   - 特征: AHNU_Project/data/AHNU_features_X.npy")
    print(f"   - 标签: AHNU_Project/data/AHNU_features_y.npy")
    
    return X, y

def main():
    """主函数"""
    try:
        # 1. 加载和预览数据
        df = load_and_preview_data()
        
        # 2. 分析循环结构
        analyze_cycle_structure(df)
        
        # 3. 演示特征提取
        df_features = demonstrate_feature_extraction()
        
        # 4. 分析SOH退化
        analyze_soh_degradation(df_features)
        
        # 5. 创建可视化
        create_visualization(df_features)
        
        # 6. 生成模型数据
        X, y = generate_model_ready_data(df_features)
        
        print(f"\n=== 特征提取完成 ===")
        print(f"数据已准备好用于PINN4SOH模型训练！")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()