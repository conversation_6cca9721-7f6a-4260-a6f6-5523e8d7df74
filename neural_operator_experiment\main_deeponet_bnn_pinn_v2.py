import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.autograd import grad
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
import argparse
import os
import random
import matplotlib.pyplot as plt
import scienceplots
import math

# BNN Imports from Blitz
import blitz.modules as bnn

# Check for GPU availability
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 0. Dataloader classes (same as v1)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class DF():
    def __init__(self,args):
        self.normalization = True
        self.normalization_method = args.normalization_method
        self.args = args

    def _3_sigma(self, Ser1):
        rule = (Ser1.mean() - 3 * Ser1.std() > Ser1) | (Ser1.mean() + 3 * Ser1.std() < Ser1)
        return np.arange(Ser1.shape[0])[rule]

    def delete_3_sigma(self,df):
        df = df.replace([np.inf, -np.inf], np.nan).dropna().reset_index(drop=True)
        out_index = list(set([i for col in df.columns for i in self._3_sigma(df[col])]))
        return df.drop(out_index, axis=0).reset_index(drop=True)

    def read_one_csv(self,file_name,nominal_capacity=None):
        df = pd.read_csv(file_name)
        df.insert(df.shape[1]-1,'cycle index',np.arange(df.shape[0], dtype=np.float64))
        df = self.delete_3_sigma(df)
        if nominal_capacity is not None:
            df['capacity'] = df['capacity']/nominal_capacity
            f_df = df.iloc[:,:-1]
            if self.normalization_method == 'z-score':
                f_df = (f_df - f_df.mean())/f_df.std()
            df.iloc[:,:-1] = f_df
        return df

    def load_one_battery(self,path,nominal_capacity=None):
        df = self.read_one_csv(path,nominal_capacity)
        x, y = df.iloc[:,:-1].values, df.iloc[:,-1].values
        return (x[:-1],y[:-1]),(x[1:],y[1:])

    def load_all_battery(self,path_list,nominal_capacity):
        X1, X2, Y1, Y2 = [], [], [], []
        for path in path_list:
            (x1, y1), (x2, y2) = self.load_one_battery(path, nominal_capacity)
            X1.append(x1); X2.append(x2); Y1.append(y1); Y2.append(y2)

        X1, X2, Y1, Y2 = [np.concatenate(d, axis=0) for d in [X1, X2, Y1, Y2]]
        tensor_X1, tensor_X2, tensor_Y1, tensor_Y2 = [torch.from_numpy(d).float() for d in [X1, X2, Y1, Y2]]
        tensor_Y1, tensor_Y2 = tensor_Y1.view(-1,1), tensor_Y2.view(-1,1)
        
        split = int(tensor_X1.shape[0] * 0.8)
        train_X1, test_X1 = tensor_X1[:split], tensor_X1[split:]
        train_X2, test_X2 = tensor_X2[:split], tensor_X2[split:]
        train_Y1, test_Y1 = tensor_Y1[:split], tensor_Y1[split:]
        train_Y2, test_Y2 = tensor_Y2[:split], tensor_Y2[split:]
        
        train_X1, valid_X1, train_X2, valid_X2, train_Y1, valid_Y1, train_Y2, valid_Y2 = \
            train_test_split(train_X1, train_X2, train_Y1, train_Y2, test_size=0.2, random_state=420)

        train_loader = DataLoader(TensorDataset(train_X1, train_X2, train_Y1, train_Y2), batch_size=self.args.batch_size, shuffle=True)
        valid_loader = DataLoader(TensorDataset(valid_X1, valid_X2, valid_Y1, valid_Y2), batch_size=self.args.batch_size, shuffle=True)
        test_loader = DataLoader(TensorDataset(test_X1, test_X2, test_Y1, test_Y2), batch_size=self.args.batch_size, shuffle=False)

        return {'train': train_loader, 'valid': valid_loader, 'test': test_loader}

class XJTUdata(DF):
    def __init__(self, root, args):
        super(XJTUdata, self).__init__(args)
        self.root = root
        self.file_list = os.listdir(root)
        self.batch_names = ['2C','3C','R2.5','R3','RW']
        self.nominal_capacity = 2.0

    def read_one_batch(self,batch='2C'):
        file_list = [os.path.join(self.root, f) for f in self.file_list if batch in f]
        return self.load_all_battery(path_list=file_list,nominal_capacity=self.nominal_capacity)

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 1. Enhanced DeepONet Implementation (v2)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class Swish(nn.Module):
    """Swish activation function: x * sigmoid(x)"""
    def forward(self, x):
        return x * torch.sigmoid(x)

class GELU_Custom(nn.Module):
    """Custom GELU implementation"""
    def forward(self, x):
        return 0.5 * x * (1 + torch.tanh(math.sqrt(2 / math.pi) * (x + 0.044715 * torch.pow(x, 3))))

class EnhancedBranchNet(nn.Module):
    """Enhanced Branch network with better architecture"""
    def __init__(self, input_dim, latent_dim, hidden_dims=[128, 256, 128], dropout_rate=0.1):
        super(EnhancedBranchNet, self).__init__()
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        
        layers = []
        prev_dim = input_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                Swish(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # Final layer without activation
        layers.append(nn.Linear(prev_dim, latent_dim))
        
        self.layers = nn.Sequential(*layers)
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                nn.init.constant_(m.bias, 0)
        
    def forward(self, x):
        return self.layers(x)

class EnhancedTrunkNet(nn.Module):
    """Enhanced Trunk network with better architecture"""
    def __init__(self, query_dim, latent_dim, hidden_dims=[64, 128, 64], dropout_rate=0.1):
        super(EnhancedTrunkNet, self).__init__()
        self.query_dim = query_dim
        self.latent_dim = latent_dim
        
        layers = []
        prev_dim = query_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                Swish(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        # Final layer without activation
        layers.append(nn.Linear(prev_dim, latent_dim))
        
        self.layers = nn.Sequential(*layers)
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                nn.init.constant_(m.bias, 0)
        
    def forward(self, y):
        return self.layers(y)

class EnhancedDeepONet(nn.Module):
    """Enhanced DeepONet with improved architecture"""
    def __init__(self, input_dim, query_dim, latent_dim=128, 
                 branch_hidden_dims=[128, 256, 128], trunk_hidden_dims=[64, 128, 64], 
                 dropout_rate=0.1):
        super(EnhancedDeepONet, self).__init__()
        self.input_dim = input_dim
        self.query_dim = query_dim
        self.latent_dim = latent_dim
        
        self.branch_net = EnhancedBranchNet(input_dim, latent_dim, branch_hidden_dims, dropout_rate)
        self.trunk_net = EnhancedTrunkNet(query_dim, latent_dim, trunk_hidden_dims, dropout_rate)
        
        # Learnable bias with better initialization
        self.bias = nn.Parameter(torch.zeros(1))
        
        # Additional output transformation
        self.output_transform = nn.Sequential(
            nn.Linear(1, 32),
            Swish(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(32, 1)
        )
        
    def forward(self, u, y):
        # Get branch and trunk outputs
        branch_out = self.branch_net(u)  # (batch_size, latent_dim)
        trunk_out = self.trunk_net(y)    # (batch_size, latent_dim)
        
        # Compute dot product and add bias
        output = torch.sum(branch_out * trunk_out, dim=1, keepdim=True) + self.bias
        
        # Apply output transformation
        output = self.output_transform(output)
        
        return output

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 2. Enhanced Bayesian Solution Network with SOH Constraints
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def get_kl_divergence(module):
    """Recursive function to fetch kl-divergence from Bayesian Modules"""
    kl_loss = torch.tensor(0.0, device=device)
    for sub_module in module.children():
        if isinstance(sub_module, bnn.BayesianLinear):
            kl_loss += sub_module.log_variational_posterior - sub_module.log_prior
        elif isinstance(sub_module, nn.Module):
            kl_loss += get_kl_divergence(sub_module)
    return kl_loss

class EnhancedBayesianMLP(nn.Module):
    def __init__(self, input_dim, output_dim, hidden_dims, dropout_rate=0.1):
        super().__init__()
        self.layers = nn.ModuleList()

        prev_dim = input_dim
        for i, hidden_dim in enumerate(hidden_dims):
            self.layers.append(bnn.BayesianLinear(prev_dim, hidden_dim))
            if i < len(hidden_dims) - 1:  # Not the last layer
                self.layers.append(nn.BatchNorm1d(hidden_dim))
                self.layers.append(Swish())
                self.layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim

        # Final layer
        if len(hidden_dims) > 0:
            self.layers.append(bnn.BayesianLinear(prev_dim, output_dim))
        else:
            self.layers.append(bnn.BayesianLinear(input_dim, output_dim))

    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        return x

class EnhancedBayesianSolution_u(nn.Module):
    """Enhanced Bayesian Solution Network with SOH constraints"""
    def __init__(self, soh_constraint_type='sigmoid'):
        super().__init__()
        # Deeper and wider architecture
        self.encoder = EnhancedBayesianMLP(
            input_dim=17,
            output_dim=64,
            hidden_dims=[80, 120, 80],
            dropout_rate=0.15
        )
        self.predictor = EnhancedBayesianMLP(
            input_dim=64,
            output_dim=1,
            hidden_dims=[48, 32],
            dropout_rate=0.1
        )

        self.soh_constraint_type = soh_constraint_type

        # Additional constraint parameters
        if soh_constraint_type == 'sigmoid_scaled':
            self.soh_scale = nn.Parameter(torch.tensor(1.0))
            self.soh_offset = nn.Parameter(torch.tensor(0.0))

    def forward(self, x):
        encoded = self.encoder(x)
        raw_output = self.predictor(encoded)

        # Apply SOH constraints
        if self.soh_constraint_type == 'sigmoid':
            # Simple sigmoid constraint: output ∈ (0, 1)
            constrained_output = torch.sigmoid(raw_output)
        elif self.soh_constraint_type == 'sigmoid_scaled':
            # Learnable scaled sigmoid: output ∈ (offset, scale + offset)
            constrained_output = torch.sigmoid(raw_output) * self.soh_scale + self.soh_offset
        elif self.soh_constraint_type == 'tanh_scaled':
            # Scaled tanh: output ∈ (0, 1)
            constrained_output = 0.5 * (torch.tanh(raw_output) + 1)
        elif self.soh_constraint_type == 'clamp':
            # Hard clamp: output ∈ [0, 1]
            constrained_output = torch.clamp(raw_output, 0, 1)
        else:
            # No constraint
            constrained_output = raw_output

        return constrained_output

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 3. Enhanced Loss Functions with SOH Constraints
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class SOHConstraintLoss(nn.Module):
    """Custom loss function with SOH constraints"""
    def __init__(self, constraint_weight=1.0):
        super().__init__()
        self.constraint_weight = constraint_weight
        self.mse = nn.MSELoss()

    def forward(self, pred, target):
        # Standard MSE loss
        mse_loss = self.mse(pred, target)

        # SOH constraint losses
        # 1. Penalty for predictions outside [0, 1]
        lower_violation = torch.relu(-pred)  # Penalty for pred < 0
        upper_violation = torch.relu(pred - 1)  # Penalty for pred > 1
        constraint_loss = (lower_violation + upper_violation).mean()

        # 2. Smoothness penalty (discourage rapid changes)
        # This is handled in the main training loop with consecutive predictions

        total_loss = mse_loss + self.constraint_weight * constraint_loss
        return total_loss, mse_loss, constraint_loss

class PhysicsConstraintLoss(nn.Module):
    """Enhanced physics constraint loss"""
    def __init__(self, monotonicity_weight=1.0, smoothness_weight=0.1):
        super().__init__()
        self.monotonicity_weight = monotonicity_weight
        self.smoothness_weight = smoothness_weight
        self.relu = nn.ReLU()

    def forward(self, u1, u2, y1, y2):
        # Original monotonicity constraint
        monotonic_loss = self.relu(torch.mul(u2 - u1, y1 - y2)).mean()

        # Additional smoothness constraint
        pred_diff = torch.abs(u2 - u1)
        target_diff = torch.abs(y2 - y1)
        smoothness_loss = torch.abs(pred_diff - target_diff).mean()

        total_physics_loss = (self.monotonicity_weight * monotonic_loss +
                            self.smoothness_weight * smoothness_loss)

        return total_physics_loss, monotonic_loss, smoothness_loss

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 4. Enhanced Main PINN Model with Advanced Training
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class EnhancedDeepONet_BNN_PINN(nn.Module):
    def __init__(self, args):
        super().__init__()
        self.args = args

        # Enhanced Bayesian solution network with SOH constraints
        self.solution_u = EnhancedBayesianSolution_u(
            soh_constraint_type=args.soh_constraint_type
        ).to(device)

        # Enhanced DeepONet for dynamics modeling
        self.dynamical_F = EnhancedDeepONet(
            input_dim=35,  # Combined input features
            query_dim=1,   # Time dimension for query
            latent_dim=args.deeponet_latent_dim,
            branch_hidden_dims=args.branch_hidden_dims,
            trunk_hidden_dims=args.trunk_hidden_dims,
            dropout_rate=args.dropout_rate
        ).to(device)

        # Enhanced loss functions
        self.soh_loss = SOHConstraintLoss(constraint_weight=args.soh_constraint_weight)
        self.physics_loss = PhysicsConstraintLoss(
            monotonicity_weight=args.monotonicity_weight,
            smoothness_weight=args.smoothness_weight
        )
        self.pde_loss = nn.MSELoss()

        # Optimizers with different learning rates
        self.optimizer_u = torch.optim.AdamW(
            self.solution_u.parameters(),
            lr=args.lr_u,
            weight_decay=args.weight_decay_u,
            betas=(0.9, 0.999)
        )
        self.optimizer_f = torch.optim.AdamW(
            self.dynamical_F.parameters(),
            lr=args.lr_f,
            weight_decay=args.weight_decay_f,
            betas=(0.9, 0.999)
        )

        # Learning rate schedulers
        self.scheduler_u = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer_u, mode='min', factor=0.8, patience=10, verbose=True
        )
        self.scheduler_f = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer_f, mode='min', factor=0.8, patience=10, verbose=True
        )

        # Training metrics tracking
        self.training_history = {
            'total_loss': [], 'data_loss': [], 'pde_loss': [], 'physics_loss': [],
            'constraint_loss': [], 'kl_loss': [], 'lr_u': [], 'lr_f': []
        }

    def forward(self, xt):
        xt.requires_grad = True
        x, t = xt[:, :-1], xt[:, -1:]

        # Get solution u from Enhanced Bayesian network (with SOH constraints)
        u = self.solution_u(torch.cat((x, t), dim=1))

        # Compute gradients
        u_t = grad(u.sum(), t, create_graph=True, only_inputs=True)[0]
        u_x = grad(u.sum(), x, create_graph=True, only_inputs=True)[0]

        # Prepare input for Enhanced DeepONet
        branch_input = torch.cat([xt, u, u_x, u_t], dim=1)

        # Enhanced trunk input processing
        t_normalized = (t - t.min()) / (t.max() - t.min() + 1e-8)
        trunk_input = t_normalized

        # Get dynamics prediction from Enhanced DeepONet
        F = self.dynamical_F(branch_input, trunk_input)

        return u, u_t - F

    def train_one_epoch(self, dataloader):
        self.train()
        epoch_metrics = {
            'total_loss': 0, 'data_loss': 0, 'pde_loss': 0, 'physics_loss': 0,
            'constraint_loss': 0, 'kl_loss': 0, 'n_batches': 0
        }

        for x1, x2, y1, y2 in dataloader:
            x1, x2, y1, y2 = [d.to(device) for d in [x1, x2, y1, y2]]

            # Forward pass
            u1, f1 = self.forward(x1)
            u2, f2 = self.forward(x2)

            # Enhanced loss computation
            # 1. SOH-constrained data loss
            soh_loss_1, mse_loss_1, constraint_loss_1 = self.soh_loss(u1, y1)
            soh_loss_2, mse_loss_2, constraint_loss_2 = self.soh_loss(u2, y2)
            loss_data = 0.5 * (soh_loss_1 + soh_loss_2)
            constraint_loss = 0.5 * (constraint_loss_1 + constraint_loss_2)

            # 2. PDE loss
            loss_pde = 0.5 * (self.pde_loss(f1, torch.zeros_like(f1)) +
                             self.pde_loss(f2, torch.zeros_like(f2)))

            # 3. Enhanced physics loss
            physics_loss_total, monotonic_loss, smoothness_loss = self.physics_loss(u1, u2, y1, y2)

            # 4. Bayesian complexity cost
            complexity_cost = get_kl_divergence(self.solution_u)

            # Total loss with enhanced weighting
            total_loss = (loss_data +
                         self.args.alpha * loss_pde +
                         self.args.beta * physics_loss_total +
                         self.args.complexity_weight * complexity_cost)

            # Optimization step
            self.optimizer_u.zero_grad()
            self.optimizer_f.zero_grad()
            total_loss.backward()

            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(self.solution_u.parameters(), max_norm=1.0)
            torch.nn.utils.clip_grad_norm_(self.dynamical_F.parameters(), max_norm=1.0)

            self.optimizer_u.step()
            self.optimizer_f.step()

            # Track metrics
            epoch_metrics['total_loss'] += total_loss.item()
            epoch_metrics['data_loss'] += loss_data.item()
            epoch_metrics['pde_loss'] += loss_pde.item()
            epoch_metrics['physics_loss'] += physics_loss_total.item()
            epoch_metrics['constraint_loss'] += constraint_loss.item()
            epoch_metrics['kl_loss'] += complexity_cost.item()
            epoch_metrics['n_batches'] += 1

        # Average metrics
        for key in epoch_metrics:
            if key != 'n_batches':
                epoch_metrics[key] /= epoch_metrics['n_batches']

        # Update learning rate schedulers
        self.scheduler_u.step(epoch_metrics['total_loss'])
        self.scheduler_f.step(epoch_metrics['total_loss'])

        # Store training history
        self.training_history['total_loss'].append(epoch_metrics['total_loss'])
        self.training_history['data_loss'].append(epoch_metrics['data_loss'])
        self.training_history['pde_loss'].append(epoch_metrics['pde_loss'])
        self.training_history['physics_loss'].append(epoch_metrics['physics_loss'])
        self.training_history['constraint_loss'].append(epoch_metrics['constraint_loss'])
        self.training_history['kl_loss'].append(epoch_metrics['kl_loss'])
        self.training_history['lr_u'].append(self.optimizer_u.param_groups[0]['lr'])
        self.training_history['lr_f'].append(self.optimizer_f.param_groups[0]['lr'])

        return epoch_metrics

    def predict_with_uncertainty(self, dataloader, n_samples):
        """Enhanced prediction with uncertainty quantification and SOH validation"""
        self.eval()
        all_true, all_preds = [], []
        soh_violations = 0
        total_predictions = 0

        with torch.no_grad():
            for x1, _, y1, _ in dataloader:
                x1, y1 = x1.to(device), y1.to(device)

                # Multiple forward passes for uncertainty estimation
                preds = []
                for _ in range(n_samples):
                    pred = self.solution_u(x1)
                    preds.append(pred.cpu().numpy())

                    # Count SOH violations
                    violations = ((pred < 0) | (pred > 1)).sum().item()
                    soh_violations += violations
                    total_predictions += pred.numel()

                all_preds.append(np.stack(preds, axis=0))
                all_true.append(y1.cpu().numpy())

        all_preds = np.concatenate(all_preds, axis=1)
        pred_mean = np.mean(all_preds, axis=0)
        pred_std = np.std(all_preds, axis=0)

        # SOH violation statistics
        violation_rate = soh_violations / total_predictions if total_predictions > 0 else 0
        print(f"SOH violation rate: {violation_rate:.4f} ({soh_violations}/{total_predictions})")

        return np.concatenate(all_true, axis=0), pred_mean, pred_std, violation_rate

    def save_training_history(self, save_path):
        """Save training history for analysis"""
        np.savez(save_path, **self.training_history)
        print(f"Training history saved to: {save_path}")

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 5. Enhanced Plotting and Analysis Functions
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def plot_enhanced_results(true, pred_mean, pred_std, save_path, violation_rate=0):
    """Enhanced plotting with more detailed analysis"""
    plt.style.use(['science', 'nature'])
    fig, axes = plt.subplots(2, 2, figsize=(12, 8), dpi=200)

    # Main prediction plot
    ax = axes[0, 0]
    ax.plot(true, label='Ground Truth', color='#403990', linewidth=2, zorder=3)
    ax.plot(pred_mean, label='DeepONet-BNN-PINN v2', color='#CF3D3E', linewidth=2, zorder=2)
    ax.fill_between(range(len(pred_mean)),
                    (pred_mean - 2*pred_std).flatten(),
                    (pred_mean + 2*pred_std).flatten(),
                    color='#FBDD85', alpha=0.5, label='95% CI', zorder=1)
    ax.axhline(y=0, color='red', linestyle='--', alpha=0.5, label='SOH=0')
    ax.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='SOH=1')
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('State of Health (SOH)')
    ax.set_title(f'Enhanced Prediction (Violation Rate: {violation_rate:.4f})')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Error analysis
    ax = axes[0, 1]
    errors = np.abs(pred_mean.flatten() - true.flatten())
    ax.plot(errors, color='#2E8B57', linewidth=1)
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('Absolute Error')
    ax.set_title(f'Prediction Error (MAE: {np.mean(errors):.6f})')
    ax.grid(True, alpha=0.3)

    # Uncertainty analysis
    ax = axes[1, 0]
    ax.plot(pred_std.flatten(), color='#FF6347', linewidth=1)
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('Prediction Uncertainty (Std)')
    ax.set_title(f'Uncertainty Distribution (Mean: {np.mean(pred_std):.6f})')
    ax.grid(True, alpha=0.3)

    # Scatter plot: True vs Predicted
    ax = axes[1, 1]
    ax.scatter(true.flatten(), pred_mean.flatten(), alpha=0.6, s=1, color='#4169E1')
    min_val, max_val = min(true.min(), pred_mean.min()), max(true.max(), pred_mean.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.set_title('True vs Predicted')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Enhanced plot saved to: {save_path}")

def plot_training_history(history_path, save_path):
    """Plot training history"""
    history = np.load(history_path)

    plt.style.use(['science', 'nature'])
    fig, axes = plt.subplots(2, 2, figsize=(12, 8), dpi=200)

    # Loss components
    ax = axes[0, 0]
    ax.plot(history['total_loss'], label='Total Loss', linewidth=2)
    ax.plot(history['data_loss'], label='Data Loss', linewidth=1)
    ax.plot(history['pde_loss'], label='PDE Loss', linewidth=1)
    ax.plot(history['physics_loss'], label='Physics Loss', linewidth=1)
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.set_title('Training Loss Components')
    ax.legend()
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)

    # Constraint and KL losses
    ax = axes[0, 1]
    ax.plot(history['constraint_loss'], label='SOH Constraint Loss', color='red', linewidth=2)
    ax.plot(history['kl_loss'], label='KL Divergence', color='orange', linewidth=2)
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.set_title('Constraint Losses')
    ax.legend()
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)

    # Learning rates
    ax = axes[1, 0]
    ax.plot(history['lr_u'], label='Solution Network LR', linewidth=2)
    ax.plot(history['lr_f'], label='DeepONet LR', linewidth=2)
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Learning Rate')
    ax.set_title('Learning Rate Schedule')
    ax.legend()
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)

    # Combined view
    ax = axes[1, 1]
    ax.plot(history['total_loss'], label='Total Loss', linewidth=2, color='black')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Total Loss')
    ax.set_title('Training Progress')
    ax.set_yscale('log')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Training history plot saved to: {save_path}")

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 6. Enhanced Arguments and Main Execution
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def get_enhanced_args():
    parser = argparse.ArgumentParser(description="Enhanced DeepONet-BNN-PINN v2 for Battery SOH")

    # Data parameters
    parser.add_argument('--data_root', type=str, default='./data/XJTU data')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=256, help='Batch size')
    parser.add_argument('--normalization_method', type=str, default='z-score')

    # Model architecture parameters
    parser.add_argument('--deeponet_latent_dim', type=int, default=128, help='DeepONet latent dimension')
    parser.add_argument('--branch_hidden_dims', type=int, nargs='+', default=[128, 256, 128],
                       help='Branch network hidden dimensions')
    parser.add_argument('--trunk_hidden_dims', type=int, nargs='+', default=[64, 128, 64],
                       help='Trunk network hidden dimensions')
    parser.add_argument('--dropout_rate', type=float, default=0.1, help='Dropout rate')

    # SOH constraint parameters
    parser.add_argument('--soh_constraint_type', type=str, default='sigmoid',
                       choices=['sigmoid', 'sigmoid_scaled', 'tanh_scaled', 'clamp', 'none'],
                       help='Type of SOH constraint')
    parser.add_argument('--soh_constraint_weight', type=float, default=10.0,
                       help='Weight for SOH constraint loss')

    # Training parameters
    parser.add_argument('--lr_u', type=float, default=1e-3, help='Learning rate for solution network')
    parser.add_argument('--lr_f', type=float, default=1e-3, help='Learning rate for DeepONet')
    parser.add_argument('--weight_decay_u', type=float, default=1e-5, help='Weight decay for solution network')
    parser.add_argument('--weight_decay_f', type=float, default=1e-5, help='Weight decay for DeepONet')

    # Loss weights
    parser.add_argument('--alpha', type=float, default=1.0, help='PDE loss weight')
    parser.add_argument('--beta', type=float, default=1.0, help='Physics loss weight')
    parser.add_argument('--complexity_weight', type=float, default=1e-4, help='BNN complexity weight')
    parser.add_argument('--monotonicity_weight', type=float, default=1.0, help='Monotonicity constraint weight')
    parser.add_argument('--smoothness_weight', type=float, default=0.1, help='Smoothness constraint weight')

    # Uncertainty quantification
    parser.add_argument('--n_samples', type=int, default=100, help='Number of samples for uncertainty estimation')

    # Logging and saving
    parser.add_argument('--save_history', action='store_true', help='Save training history')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')

    return parser.parse_args()

if __name__ == "__main__":
    print("🚀 Starting Enhanced DeepONet-BNN-PINN v2 for Battery SOH Prediction")
    args = get_enhanced_args()

    print(f"Device: {device}")
    if args.verbose:
        print(f"Arguments: {args}")

    # Create directories
    base_dir = './neural_operator_experiment'
    results_dir = os.path.join(base_dir, 'results_v2')
    plots_dir = os.path.join(base_dir, 'plots_v2')
    history_dir = os.path.join(base_dir, 'history_v2')
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(plots_dir, exist_ok=True)
    os.makedirs(history_dir, exist_ok=True)

    xjtu_batches = ['2C', '3C', 'R2.5', 'R3', 'RW']
    all_true, all_pred_mean, all_pred_std = [], [], []
    all_violation_rates = []

    print(f"\n📊 Training Configuration:")
    print(f"  - SOH Constraint: {args.soh_constraint_type} (weight: {args.soh_constraint_weight})")
    print(f"  - DeepONet Latent Dim: {args.deeponet_latent_dim}")
    print(f"  - Branch Hidden Dims: {args.branch_hidden_dims}")
    print(f"  - Trunk Hidden Dims: {args.trunk_hidden_dims}")
    print(f"  - Dropout Rate: {args.dropout_rate}")
    print(f"  - Learning Rates: u={args.lr_u}, f={args.lr_f}")

    for batch_name in xjtu_batches:
        print(f"\n{'='*50}")
        print(f"🔋 PROCESSING BATCH: {batch_name}")
        print(f"{'='*50}")

        # Load data
        data_loader_class = XJTUdata(root=args.data_root, args=args)
        loaders = data_loader_class.read_one_batch(batch=batch_name)
        train_loader, valid_loader, test_loader = loaders['train'], loaders['valid'], loaders['test']

        # Create enhanced model
        model = EnhancedDeepONet_BNN_PINN(args).to(device)
        model_save_path = os.path.join(results_dir, f'enhanced_deeponet_bnn_model_{batch_name}.pth')
        history_save_path = os.path.join(history_dir, f'training_history_{batch_name}.npz')

        print(f"🏋️  Starting training for {args.epochs} epochs...")

        # Training loop with progress tracking
        best_loss = float('inf')
        patience_counter = 0
        patience_limit = 20

        for epoch in range(1, args.epochs + 1):
            epoch_metrics = model.train_one_epoch(train_loader)

            # Early stopping check
            if epoch_metrics['total_loss'] < best_loss:
                best_loss = epoch_metrics['total_loss']
                patience_counter = 0
                # Save best model
                torch.save(model.state_dict(), model_save_path)
            else:
                patience_counter += 1

            # Progress reporting
            if epoch % 10 == 0 or epoch == 1:
                print(f"  Epoch {epoch:3d}/{args.epochs}: "
                      f"Loss={epoch_metrics['total_loss']:.6f}, "
                      f"Data={epoch_metrics['data_loss']:.6f}, "
                      f"PDE={epoch_metrics['pde_loss']:.6f}, "
                      f"Physics={epoch_metrics['physics_loss']:.6f}, "
                      f"Constraint={epoch_metrics['constraint_loss']:.6f}")

            # Early stopping
            if patience_counter >= patience_limit:
                print(f"  Early stopping at epoch {epoch} (patience exceeded)")
                break

        print(f"✅ Training completed! Best model saved to: {model_save_path}")

        # Save training history
        if args.save_history:
            model.save_training_history(history_save_path)

        # Testing with uncertainty quantification
        print(f"🧪 Testing with uncertainty quantification...")
        model.load_state_dict(torch.load(model_save_path))  # Load best model
        true_labels, pred_mean, pred_std, violation_rate = model.predict_with_uncertainty(
            test_loader, n_samples=args.n_samples
        )

        all_true.append(true_labels)
        all_pred_mean.append(pred_mean)
        all_pred_std.append(pred_std)
        all_violation_rates.append(violation_rate)

        # Calculate and display metrics
        mse = np.mean((pred_mean.flatten() - true_labels.flatten()) ** 2)
        mae = np.mean(np.abs(pred_mean.flatten() - true_labels.flatten()))
        r2 = 1 - np.sum((true_labels.flatten() - pred_mean.flatten()) ** 2) / np.sum((true_labels.flatten() - np.mean(true_labels)) ** 2)

        print(f"📈 Batch {batch_name} Results:")
        print(f"  - MSE: {mse:.8f}")
        print(f"  - MAE: {mae:.8f}")
        print(f"  - R²:  {r2:.6f}")
        print(f"  - SOH Violation Rate: {violation_rate:.4f}")

    # Aggregate results
    print(f"\n{'='*50}")
    print(f"📊 AGGREGATING ALL RESULTS")
    print(f"{'='*50}")

    final_true = np.concatenate(all_true, axis=0)
    final_pred_mean = np.concatenate(all_pred_mean, axis=0)
    final_pred_std = np.concatenate(all_pred_std, axis=0)
    avg_violation_rate = np.mean(all_violation_rates)

    # Save aggregated results
    np.save(os.path.join(results_dir, "true_labels_all_v2.npy"), final_true)
    np.save(os.path.join(results_dir, "pred_mean_all_v2.npy"), final_pred_mean)
    np.save(os.path.join(results_dir, "pred_std_all_v2.npy"), final_pred_std)

    # Final metrics
    final_mse = np.mean((final_pred_mean.flatten() - final_true.flatten()) ** 2)
    final_mae = np.mean(np.abs(final_pred_mean.flatten() - final_true.flatten()))
    final_r2 = 1 - np.sum((final_true.flatten() - final_pred_mean.flatten()) ** 2) / np.sum((final_true.flatten() - np.mean(final_true)) ** 2)

    print(f"🎯 Final Overall Results:")
    print(f"  - MSE: {final_mse:.8f}")
    print(f"  - MAE: {final_mae:.8f}")
    print(f"  - R²:  {final_r2:.6f}")
    print(f"  - Average SOH Violation Rate: {avg_violation_rate:.4f}")

    # Generate enhanced plots
    plot_save_path = os.path.join(plots_dir, 'enhanced_prediction_all_batches_v2.png')
    plot_enhanced_results(final_true, final_pred_mean, final_pred_std, plot_save_path, avg_violation_rate)

    print(f"\n🎉 Enhanced DeepONet-BNN-PINN v2 completed successfully!")
    print(f"📁 Results saved in: {results_dir}")
    print(f"📊 Plots saved in: {plots_dir}")
    if args.save_history:
        print(f"📈 Training history saved in: {history_dir}")
