AHNU数据集分析报告
==================================================
数据形状: (216, 9)
列名: ['循环号', '充电比容量(mAh/g)', '放电比容量(mAh/g)', '充放电效率(%)', '中值电压(V)', '充电时间(h)', '放电时间(h)', '充电平均电压(V)', '放电平均电压(V)']
数据类型:
循环号               int64
充电比容量(mAh/g)    float64
放电比容量(mAh/g)    float64
充放电效率(%)        float64
中值电压(V)         float64
充电时间(h)         float64
放电时间(h)         float64
充电平均电压(V)       float64
放电平均电压(V)       float64
dtype: object
基本统计:
           循环号  ...   放电平均电压(V)
count  216.000  ...  216.000000
mean   108.500  ...    0.458556
std     62.498  ...    0.038118
min      1.000  ...    0.227000
25%     54.750  ...    0.450575
50%    108.500  ...    0.455400
75%    162.250  ...    0.471250
max    216.000  ...    0.843900

[8 rows x 9 columns]
