# AHNU项目实验代码存档

## 📁 文件夹结构

```
archive_experimental_codes/
├── data_analysis/           # 数据分析和探索代码
├── model_experiments/       # 模型实验和对比代码  
├── testing_diagnostics/     # 测试和诊断工具
└── README.md               # 本说明文件
```

## 🗂️ 各文件夹内容

### data_analysis/ (数据分析)
- `analyze_ahnu_data.py` - AHNU数据探索性分析
- `analyze_soh_calculation.py` - SOH计算方法分析

### model_experiments/ (模型实验)
- `main_deeponet_bnn_pinn_v3.py` - DeepONet-BNN-PINN v3 (效果差，过拟合)
- `main_deeponet_bnn_pinn_v4_simple.py` - DeepONet-BNN-PINN v4简化版  
- `corrected_full_pinn_test.py` - 早期PINN修正版本
- `fixed_full_pinn_test.py` - PINN修复版本
- `simple_pinn_test.py` - 简单PINN测试
- `test_full_pinn.py` - 完整PINN测试
- `baseline_comparison.py` - 基线模型对比
- `compare_basic_models.py` - 基础模型对比
- `ahnu_optimal_solution_old.py` - 旧版最优解决方案

### testing_diagnostics/ (测试诊断)
- `diagnose_ahnu_issues.py` - AHNU数据问题诊断
- `test_ahnu_data_loading.py` - 数据加载测试
- `check_xjtu_data.py` - XJTU数据检查
- `plot_pinn_results.py` - PINN结果可视化

## ⚠️ 注意事项

这些代码主要用于：
1. **实验探索**: 各种模型架构的尝试
2. **问题诊断**: 数据和模型问题的分析工具
3. **效果对比**: 不同方法的性能比较
4. **开发调试**: 开发过程中的辅助工具

大部分代码存在以下问题：
- 在小数据集上过拟合严重
- 模型复杂度与数据量不匹配
- 实验性质，不适合生产环境

## 🎯 推荐使用核心代码

保留在主目录的高效代码：
- `ahnu_3features_optimal.py` - **最佳选择** (R²=1.0)
- `ahnu_17features_version.py` - 特征工程版本
- `truly_fixed_full_pinn_test.py` - **研究推荐** (R²=0.88, 物理约束)

根据AHNU项目应用建议：
- **生产环境**: 使用 `ahnu_3features_optimal.py`
- **研究目的**: 使用 `truly_fixed_full_pinn_test.py`
- **避免使用**: 存档文件夹中的复杂模型

## 📊 模型效果对比

| 模型类型 | 文件位置 | R² | 复杂度 | 推荐度 |
|---------|----------|----|---------|---------| 
| 线性回归 | 主目录 | 1.000 | 低 | ⭐⭐⭐⭐⭐ |
| 简化PINN | 主目录 | 0.882 | 中 | ⭐⭐⭐⭐ |
| DeepONet-BNN | 存档 | <0 | 高 | ❌ |
| 其他实验 | 存档 | 变化 | 各异 | ⭐⭐ |