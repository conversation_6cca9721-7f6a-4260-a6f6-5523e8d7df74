#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试两阶段训练策略
使用XJTU数据验证先训练再冻结权重的训练方法
对比单阶段训练和两阶段训练的效果
"""

import sys
import os
sys.path.append('e:\\gitrepo\\PINN4SOH')

from dataloader.dataloader import XJTUdata
from Model.Model import PINN
import torch
import numpy as np
import argparse
from utils.util import eval_metrix
import matplotlib.pyplot as plt

def load_xjtu_data(batch_name='2C'):
    """加载XJTU数据"""
    args = argparse.Namespace()
    args.normalization_method = 'min-max'
    
    root = 'e:\\gitrepo\\PINN4SOH\\data\\XJTU data'
    data = XJTUdata(root=root, args=args)
    
    train_list = []
    test_list = []
    files = os.listdir(root)
    
    for file in files:
        if batch_name in file:
            if '4' in file or '8' in file:
                test_list.append(os.path.join(root, file))
            else:
                train_list.append(os.path.join(root, file))
    
    print(f"📊 XJTU {batch_name} 数据:")
    print(f"  训练文件: {len(train_list)} 个")
    print(f"  测试文件: {len(test_list)} 个")
    
    train_loader = data.read_all(specific_path_list=train_list)
    test_loader = data.read_all(specific_path_list=test_list)
    
    return {
        'train': train_loader['train_2'],
        'valid': train_loader['valid_2'], 
        'test': test_loader['test_3']
    }

class TwoStagePINN(PINN):
    """两阶段训练的PINN模型"""
    
    def __init__(self, args):
        super().__init__(args)
        self.stage1_history = {'epoch': [], 'loss': []}
        self.stage2_history = {'epoch': [], 'loss': []}
    
    def freeze_dynamical_f(self):
        """冻结dynamical_F的参数"""
        for param in self.dynamical_F.parameters():
            param.requires_grad = False
        print("🧊 已冻结 dynamical_F 网络参数")
    
    def unfreeze_dynamical_f(self):
        """解冻dynamical_F的参数"""
        for param in self.dynamical_F.parameters():
            param.requires_grad = True
        print("🔥 已解冻 dynamical_F 网络参数")
    
    def stage1_train_one_epoch(self, epoch, dataloader):
        """阶段一训练：联合训练两个网络"""
        return super().train_one_epoch(epoch, dataloader)
    
    def stage2_train_one_epoch(self, epoch, dataloader):
        """阶段二训练：只训练solution_u"""
        self.solution_u.train()
        # dynamical_F保持评估模式
        self.dynamical_F.eval()
        
        loss1_meter = AverageMeter()
        loss2_meter = AverageMeter()
        loss3_meter = AverageMeter()
        
        for iter, (x1, x2, y1, y2) in enumerate(dataloader):
            x1, x2, y1, y2 = x1.to(device), x2.to(device), y1.to(device), y2.to(device)
            
            u1, f1 = self.forward(x1)
            u2, f2 = self.forward(x2)
            
            # data loss
            loss1 = 0.5 * self.loss_func(u1, y1) + 0.5 * self.loss_func(u2, y2)
            
            # PDE loss
            f_target = torch.zeros_like(f1)
            loss2 = 0.5 * self.loss_func(f1, f_target) + 0.5 * self.loss_func(f2, f_target)
            
            # physics loss  
            loss3 = self.relu(torch.mul(u2 - u1, y1 - y2)).sum()
            
            # total loss
            loss = loss1 + self.alpha * loss2 + self.beta * loss3
            
            # 只更新solution_u的参数
            self.optimizer1.zero_grad()
            loss.backward()
            self.optimizer1.step()
            
            loss1_meter.update(loss1.item())
            loss2_meter.update(loss2.item())
            loss3_meter.update(loss3.item())
            
        return loss1_meter.avg, loss2_meter.avg, loss3_meter.avg
    
    def two_stage_train(self, trainloader, validloader=None, testloader=None, 
                       stage1_epochs=100, stage2_epochs=50, finetune_lr=5e-4):
        """两阶段训练"""
        print(f"\\n🎯 开始两阶段训练")
        print(f"阶段一: 联合训练 ({stage1_epochs} 轮)")
        print(f"阶段二: 冻结dynamical_F，微调solution_u ({stage2_epochs} 轮)")
        print("=" * 60)
        
        # ===============================
        # 阶段一: 联合训练
        # ===============================
        print(f"\\n💪 阶段一: 联合训练 solution_u + dynamical_F")
        
        min_valid_mse = float('inf')
        early_stop = 0
        stage1_best_model = None
        
        for e in range(1, stage1_epochs + 1):
            early_stop += 1
            loss1, loss2, loss3 = self.stage1_train_one_epoch(e, trainloader)
            current_lr = self.scheduler.step()
            
            total_loss = loss1 + self.alpha * loss2 + self.beta * loss3
            self.stage1_history['epoch'].append(e)
            self.stage1_history['loss'].append(total_loss)
            
            info = f'[阶段一] epoch:{e:3d}, lr:{current_lr:.6f}, loss:{total_loss:.6f}'
            self.logger.info(info)
            
            if e % 10 == 0:
                print(f"  阶段一 {e:3d}/{stage1_epochs}: 损失={total_loss:.6f}")
            
            if validloader is not None:
                valid_mse = self.Valid(validloader)
                if valid_mse < min_valid_mse:
                    min_valid_mse = valid_mse
                    stage1_best_model = {
                        'solution_u': self.solution_u.state_dict(),
                        'dynamical_F': self.dynamical_F.state_dict()
                    }
                    early_stop = 0
            
            if early_stop > 20:
                print(f"  阶段一在第{e}轮早停")
                break
        
        print(f"✅ 阶段一完成! 最佳验证MSE: {min_valid_mse:.6f}")
        
        # 加载阶段一最佳模型
        if stage1_best_model:
            self.solution_u.load_state_dict(stage1_best_model['solution_u'])
            self.dynamical_F.load_state_dict(stage1_best_model['dynamical_F'])\n        \n        # ===============================\n        # 阶段二: 微调solution_u\n        # ===============================\n        print(f\"\\n🧊 阶段二: 冻结dynamical_F，微调solution_u\")\n        \n        # 冻结dynamical_F\n        self.freeze_dynamical_f()\n        \n        # 设置更小的学习率用于微调\n        for param_group in self.optimizer1.param_groups:\n            param_group['lr'] = finetune_lr\n        print(f\"📋 微调学习率设为: {finetune_lr}\")\n        \n        min_valid_mse = float('inf')\n        early_stop = 0\n        \n        for e in range(1, stage2_epochs + 1):\n            early_stop += 1\n            loss1, loss2, loss3 = self.stage2_train_one_epoch(e, trainloader)\n            \n            total_loss = loss1 + self.alpha * loss2 + self.beta * loss3\n            self.stage2_history['epoch'].append(e)\n            self.stage2_history['loss'].append(total_loss)\n            \n            info = f'[阶段二] epoch:{e:3d}, loss:{total_loss:.6f}'\n            self.logger.info(info)\n            \n            if e % 5 == 0:\n                print(f\"  阶段二 {e:3d}/{stage2_epochs}: 损失={total_loss:.6f}\")\n            \n            if validloader is not None:\n                valid_mse = self.Valid(validloader)\n                if valid_mse < min_valid_mse:\n                    min_valid_mse = valid_mse\n                    early_stop = 0\n                    # 保存最佳模型\n                    self.best_model = {\n                        'solution_u': self.solution_u.state_dict(),\n                        'dynamical_F': self.dynamical_F.state_dict()\n                    }\n            \n            if early_stop > 15:  # 微调阶段耐心值可以小一些\n                print(f\"  阶段二在第{e}轮早停\")\n                break\n        \n        print(f\"✅ 阶段二完成! 最终验证MSE: {min_valid_mse:.6f}\")\n        \n        # 解冻参数用于测试\n        self.unfreeze_dynamical_f()\n        \n        # 最终测试\n        if testloader is not None:\n            true_label, pred_label = self.Test(testloader)\n            [MAE, MAPE, MSE, RMSE] = eval_metrix(pred_label, true_label)\n            print(f\"\\n📈 两阶段训练最终结果:\")\n            print(f\"  MSE: {MSE:.8f}, MAE: {MAE:.6f}, MAPE: {MAPE:.6f}, RMSE: {RMSE:.6f}\")\n            \n            return {'MSE': MSE, 'MAE': MAE, 'MAPE': MAPE, 'RMSE': RMSE}\n    \n    def plot_training_history(self, save_path):\n        \"\"\"绘制训练历史\"\"\"\n        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))\n        \n        # 阶段一损失曲线\n        ax1.plot(self.stage1_history['epoch'], self.stage1_history['loss'], \n                'b-', label='阶段一 (联合训练)', linewidth=2)\n        ax1.set_xlabel('Epoch')\n        ax1.set_ylabel('Loss')\n        ax1.set_title('阶段一: 联合训练')\n        ax1.legend()\n        ax1.grid(True, alpha=0.3)\n        \n        # 阶段二损失曲线\n        if self.stage2_history['epoch']:\n            ax2.plot(self.stage2_history['epoch'], self.stage2_history['loss'],\n                    'r-', label='阶段二 (微调solution_u)', linewidth=2)\n            ax2.set_xlabel('Epoch')\n            ax2.set_ylabel('Loss')\n            ax2.set_title('阶段二: 微调solution_u (dynamical_F冻结)')\n            ax2.legend()\n            ax2.grid(True, alpha=0.3)\n        \n        plt.tight_layout()\n        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n        print(f\"训练历史图已保存: {save_path}\")\n\ndef compare_training_strategies():\n    \"\"\"对比单阶段和两阶段训练策略\"\"\"\n    print(\"🔬 对比单阶段 vs 两阶段训练策略\")\n    print(\"=\" * 60)\n    \n    # 加载数据\n    dataloader = load_xjtu_data('2C')\n    \n    # 基础参数\n    args = argparse.Namespace()\n    args.batch_size = 128\n    args.normalization_method = 'min-max'\n    args.epochs = 100\n    args.early_stop = 15\n    args.warmup_epochs = 20\n    args.warmup_lr = 0.001\n    args.lr = 0.005\n    args.final_lr = 0.0001\n    args.lr_F = 0.001\n    args.F_layers_num = 3\n    args.F_hidden_dim = 60\n    args.alpha = 0.7\n    args.beta = 0.2\n    args.log_dir = None\n    args.save_folder = None\n    \n    results = {}\n    \n    # ===============================\n    # 1. 传统单阶段训练\n    # ===============================\n    print(\"\\n🏃 测试1: 传统单阶段训练\")\n    print(\"-\" * 40)\n    \n    model1 = PINN(args)\n    model1.Train(trainloader=dataloader['train'], \n                validloader=dataloader['valid'],\n                testloader=dataloader['test'])\n    \n    true_label, pred_label = model1.Test(dataloader['test'])\n    [MAE, MAPE, MSE, RMSE] = eval_metrix(pred_label, true_label)\n    results['单阶段'] = {'MSE': MSE, 'MAE': MAE, 'MAPE': MAPE, 'RMSE': RMSE}\n    \n    print(f\"单阶段训练结果: MSE={MSE:.8f}, MAE={MAE:.6f}\")\n    \n    # ===============================\n    # 2. 两阶段训练\n    # ===============================\n    print(\"\\n🎯 测试2: 两阶段训练\")\n    print(\"-\" * 40)\n    \n    model2 = TwoStagePINN(args)\n    results['两阶段'] = model2.two_stage_train(\n        trainloader=dataloader['train'],\n        validloader=dataloader['valid'], \n        testloader=dataloader['test'],\n        stage1_epochs=60,  # 阶段一\n        stage2_epochs=40,  # 阶段二\n        finetune_lr=5e-4   # 微调学习率\n    )\n    \n    # ===============================\n    # 3. 结果对比\n    # ===============================\n    print(\"\\n📊 训练策略对比结果:\")\n    print(\"=\" * 60)\n    \n    print(f\"{'策略':<10} {'MSE':<12} {'MAE':<10} {'MAPE':<10} {'RMSE':<10}\")\n    print(\"-\" * 60)\n    \n    for strategy, metrics in results.items():\n        print(f\"{strategy:<10} {metrics['MSE']:<12.8f} {metrics['MAE']:<10.6f} \"\n              f\"{metrics['MAPE']:<10.6f} {metrics['RMSE']:<10.6f}\")\n    \n    # 计算改进程度\n    if '单阶段' in results and '两阶段' in results:\n        mse_improvement = (results['单阶段']['MSE'] - results['两阶段']['MSE']) / results['单阶段']['MSE'] * 100\n        mae_improvement = (results['单阶段']['MAE'] - results['两阶段']['MAE']) / results['单阶段']['MAE'] * 100\n        \n        print(f\"\\n📈 两阶段训练改进:\")\n        print(f\"  MSE 改进: {mse_improvement:+.2f}%\")\n        print(f\"  MAE 改进: {mae_improvement:+.2f}%\")\n        \n        if mse_improvement > 0:\n            print(f\"✅ 两阶段训练效果更好!\")\n        else:\n            print(f\"⚠️ 单阶段训练效果更好\")\n    \n    # 保存训练历史图\n    if hasattr(model2, 'plot_training_history'):\n        model2.plot_training_history('e:\\\\gitrepo\\\\PINN4SOH\\\\AHNU_Project\\\\plots\\\\two_stage_training_history.png')\n    \n    return results\n\nif __name__ == \"__main__\":\n    # 导入必要的工具类\n    from utils.util import AverageMeter\n    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n    \n    print(f\"🚀 两阶段训练策略验证\")\n    print(f\"设备: {device}\")\n    \n    try:\n        results = compare_training_strategies()\n        print(f\"\\n🎉 对比测试完成!\")\n    except Exception as e:\n        print(f\"❌ 测试过程中出现错误: {e}\")\n        import traceback\n        traceback.print_exc()\n