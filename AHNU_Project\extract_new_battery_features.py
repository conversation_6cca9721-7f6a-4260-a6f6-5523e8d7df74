"""
新电池数据特征提取脚本
从**********到**********提取17维特征用于PINN4SOH模型

特征说明：
1-8维：电压特征（均值、标准差、峰度、偏度、CC_Q、CC_time、斜率、熵）
9-16维：电流特征（均值、标准差、峰度、偏度、CV_Q、CV_time、斜率、熵）
17维：SOH标签（当前容量/初始容量）

作者：AI Assistant
日期：2025-01-27
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt
import os
from tqdm import tqdm

def calculate_entropy(data):
    """计算熵值"""
    if len(data) == 0:
        return 0
    # 将数据分成bins计算概率分布
    hist, _ = np.histogram(data, bins=50, density=True)
    hist = hist[hist > 0]  # 移除零值
    if len(hist) == 0:
        return 0
    return -np.sum(hist * np.log(hist))

def calculate_slope(time_data, value_data):
    """计算斜率"""
    if len(time_data) < 2 or len(value_data) < 2:
        return 0
    try:
        slope, _ = np.polyfit(time_data, value_data, 1)
        return slope
    except:
        return 0

def extract_features_from_cycle(cycle_data, initial_capacity):
    """从单个循环数据中提取17维特征"""
    
    # 分离充电和放电数据
    charge_data = cycle_data[cycle_data['电流(mA)'] > 0]  # 充电（正电流）
    discharge_data = cycle_data[cycle_data['电流(mA)'] < 0]  # 放电（负电流）
    
    # 如果没有充电或放电数据，使用全部数据
    if len(charge_data) == 0:
        charge_data = cycle_data
    if len(discharge_data) == 0:
        discharge_data = cycle_data
    
    features = {}
    
    # 电压特征 (1-8维)
    voltage_all = cycle_data['电压(V)'].values
    features['voltage_mean'] = np.mean(voltage_all)
    features['voltage_std'] = np.std(voltage_all)
    features['voltage_kurtosis'] = stats.kurtosis(voltage_all)
    features['voltage_skewness'] = stats.skew(voltage_all)
    
    # CC_Q: 恒流充电容量
    if len(charge_data) > 0:
        features['CC_Q'] = charge_data['比容量(mAh/g)'].max() - charge_data['比容量(mAh/g)'].min()
    else:
        features['CC_Q'] = 0
    
    # CC_time: 恒流充电时间
    if len(charge_data) > 0:
        features['CC_time'] = charge_data['总时间(h)'].max() - charge_data['总时间(h)'].min()
    else:
        features['CC_time'] = 0
    
    # 电压斜率
    features['voltage_slope'] = calculate_slope(cycle_data['总时间(h)'].values, voltage_all)
    
    # 电压熵
    features['voltage_entropy'] = calculate_entropy(voltage_all)
    
    # 电流特征 (9-16维)
    current_all = cycle_data['电流(mA)'].values / 1000.0  # 转换为A
    features['current_mean'] = np.mean(current_all)
    features['current_std'] = np.std(current_all)
    features['current_kurtosis'] = stats.kurtosis(current_all)
    features['current_skewness'] = stats.skew(current_all)
    
    # CV_Q: 恒压放电容量
    if len(discharge_data) > 0:
        features['CV_Q'] = discharge_data['比容量(mAh/g)'].max() - discharge_data['比容量(mAh/g)'].min()
    else:
        features['CV_Q'] = 0
    
    # CV_time: 恒压放电时间
    if len(discharge_data) > 0:
        features['CV_time'] = discharge_data['总时间(h)'].max() - discharge_data['总时间(h)'].min()
    else:
        features['CV_time'] = 0
    
    # 电流斜率
    features['current_slope'] = calculate_slope(cycle_data['总时间(h)'].values, current_all)
    
    # 电流熵
    features['current_entropy'] = calculate_entropy(current_all)
    
    # SOH计算 (17维)
    current_capacity = cycle_data['比容量(mAh/g)'].max()
    features['capacity'] = current_capacity / initial_capacity if initial_capacity > 0 else 1.0
    
    return features

def process_single_battery(csv_path, battery_name):
    """处理单个电池文件"""
    print(f"🔋 处理电池: {battery_name}")
    
    # 读取数据
    df = pd.read_csv(csv_path)
    print(f"   - 数据形状: {df.shape}")
    print(f"   - 循环数量: {len(df['循环号'].unique())}")
    
    # 获取初始容量（第一个循环的最大容量）
    first_cycle = df[df['循环号'] == 1]
    initial_capacity = first_cycle['比容量(mAh/g)'].max()
    print(f"   - 初始容量: {initial_capacity:.2f} mAh/g")
    
    # 提取每个循环的特征
    cycles = sorted(df['循环号'].unique())
    features_list = []
    
    for cycle_num in tqdm(cycles, desc=f"   提取{battery_name}特征"):
        cycle_data = df[df['循环号'] == cycle_num]
        
        # 跳过数据点太少的循环
        if len(cycle_data) < 10:
            continue
            
        features = extract_features_from_cycle(cycle_data, initial_capacity)
        features['cycle'] = cycle_num
        features['battery'] = battery_name
        features_list.append(features)
    
    # 转换为DataFrame
    features_df = pd.DataFrame(features_list)
    
    print(f"   - 提取特征: {len(features_df)} 个循环")
    print(f"   - SOH范围: {features_df['capacity'].min():.4f} - {features_df['capacity'].max():.4f}")
    
    return features_df

def extract_all_batteries():
    """提取所有电池的特征"""
    print("🚀 开始提取新电池数据特征")
    print("=" * 60)
    
    data_dir = './data/电池数据-0.01-3V'
    battery_files = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>', 
        '<EMAIL>', '<EMAIL>'
    ]
    
    all_features = []
    
    for battery_file in battery_files:
        csv_path = os.path.join(data_dir, battery_file)
        battery_name = battery_file.replace('.csv', '')
        
        if os.path.exists(csv_path):
            features_df = process_single_battery(csv_path, battery_name)
            all_features.append(features_df)
        else:
            print(f"⚠️  文件不存在: {csv_path}")
    
    if not all_features:
        print("❌ 没有找到任何数据文件")
        return None
    
    # 合并所有电池的特征
    combined_features = pd.concat(all_features, ignore_index=True)
    
    print(f"\n📊 总体统计:")
    print(f"   - 总电池数: {len(all_features)}")
    print(f"   - 总循环数: {len(combined_features)}")
    print(f"   - 特征维度: {len(combined_features.columns) - 2} (除去cycle和battery列)")
    
    return combined_features

def save_features_for_training(features_df):
    """保存特征用于训练"""
    print(f"\n💾 保存特征数据...")
    
    # 重新排列列顺序，确保与原始Model格式一致
    feature_columns = [
        'voltage_mean', 'voltage_std', 'voltage_kurtosis', 'voltage_skewness',
        'CC_Q', 'CC_time', 'voltage_slope', 'voltage_entropy',
        'current_mean', 'current_std', 'current_kurtosis', 'current_skewness',
        'CV_Q', 'CV_time', 'current_slope', 'current_entropy'
    ]
    
    # 为每个电池单独保存文件（用于训练/测试分割）
    batteries = features_df['battery'].unique()
    
    for battery in batteries:
        battery_data = features_df[features_df['battery'] == battery].copy()
        
        # 选择需要的列并重命名
        output_data = battery_data[feature_columns + ['capacity']].copy()
        
        # 保存文件
        output_file = f'./data/processed_{battery}.csv'
        output_data.to_csv(output_file, index=False)
        print(f"   - 已保存: {output_file} ({len(output_data)} 个循环)")
    
    # 也保存一个合并的文件用于分析
    combined_output = features_df[feature_columns + ['capacity', 'battery', 'cycle']].copy()
    combined_file = './data/all_batteries_features.csv'
    combined_output.to_csv(combined_file, index=False)
    print(f"   - 已保存合并文件: {combined_file}")
    
    return batteries

def create_summary_visualization(features_df):
    """创建汇总可视化"""
    print(f"\n📊 生成可视化图表...")
    
    plt.style.use('default')
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('New Battery Data Feature Analysis', fontsize=16)
    
    batteries = features_df['battery'].unique()
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    
    # SOH退化曲线
    ax = axes[0, 0]
    for i, battery in enumerate(batteries):
        battery_data = features_df[features_df['battery'] == battery]
        ax.plot(battery_data['cycle'], battery_data['capacity'], 
                color=colors[i % len(colors)], label=battery, linewidth=2)
    ax.set_title('SOH Degradation Curves')
    ax.set_xlabel('Cycle Number')
    ax.set_ylabel('SOH (Capacity Ratio)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 电压均值变化
    ax = axes[0, 1]
    for i, battery in enumerate(batteries):
        battery_data = features_df[features_df['battery'] == battery]
        ax.plot(battery_data['cycle'], battery_data['voltage_mean'], 
                color=colors[i % len(colors)], label=battery, linewidth=2)
    ax.set_title('Voltage Mean Evolution')
    ax.set_xlabel('Cycle Number')
    ax.set_ylabel('Voltage Mean (V)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 电流均值变化
    ax = axes[0, 2]
    for i, battery in enumerate(batteries):
        battery_data = features_df[features_df['battery'] == battery]
        ax.plot(battery_data['cycle'], battery_data['current_mean'], 
                color=colors[i % len(colors)], label=battery, linewidth=2)
    ax.set_title('Current Mean Evolution')
    ax.set_xlabel('Cycle Number')
    ax.set_ylabel('Current Mean (A)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # SOH分布
    ax = axes[1, 0]
    ax.hist(features_df['capacity'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax.set_title('SOH Distribution')
    ax.set_xlabel('SOH (Capacity Ratio)')
    ax.set_ylabel('Frequency')
    ax.grid(True, alpha=0.3)
    
    # 电池寿命对比
    ax = axes[1, 1]
    battery_lifes = []
    battery_names = []
    for battery in batteries:
        battery_data = features_df[features_df['battery'] == battery]
        battery_lifes.append(len(battery_data))
        battery_names.append(battery)
    
    bars = ax.bar(battery_names, battery_lifes, color=colors[:len(batteries)])
    ax.set_title('Battery Cycle Life Comparison')
    ax.set_xlabel('Battery')
    ax.set_ylabel('Number of Cycles')
    ax.tick_params(axis='x', rotation=45)
    
    # 在柱状图上添加数值
    for bar, life in zip(bars, battery_lifes):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                str(life), ha='center', va='bottom')
    
    # 特征相关性（选择几个主要特征）
    ax = axes[1, 2]
    selected_features = ['voltage_mean', 'voltage_std', 'current_mean', 'current_std', 'capacity']
    corr_matrix = features_df[selected_features].corr()
    
    im = ax.imshow(corr_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
    ax.set_title('Feature Correlation Matrix')
    ax.set_xticks(range(len(selected_features)))
    ax.set_yticks(range(len(selected_features)))
    ax.set_xticklabels(selected_features, rotation=45)
    ax.set_yticklabels(selected_features)
    
    # 添加相关系数文本
    for i in range(len(selected_features)):
        for j in range(len(selected_features)):
            text = ax.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',
                          ha="center", va="center", color="black", fontsize=8)
    
    plt.tight_layout()
    
    # 保存图表
    plot_file = './plots_new_batteries/feature_analysis.png'
    os.makedirs('./plots_new_batteries', exist_ok=True)
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"   - 图表已保存: {plot_file}")
    
    plt.show()

if __name__ == "__main__":
    print("🔋 新电池数据特征提取工具")
    print("=" * 80)
    
    # 提取特征
    features_df = extract_all_batteries()
    
    if features_df is not None:
        # 保存特征
        batteries = save_features_for_training(features_df)
        
        # 创建可视化
        create_summary_visualization(features_df)
        
        print(f"\n🎉 特征提取完成!")
        print(f"📁 处理的电池: {list(batteries)}")
        print(f"📊 总循环数: {len(features_df)}")
        print(f"🔧 下一步: 使用这些特征文件进行PINN训练")
        
    else:
        print("❌ 特征提取失败")
