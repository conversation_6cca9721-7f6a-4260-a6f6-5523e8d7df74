#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XJTU 17维特征提取脚本
从cycledata.csv中提取每个cycle的16维特征 + SOH标签

特征说明：
- 前8维：电压相关特征（均值、标准差、峰度、偏度、CC_Q、CC_time、斜率、熵）
- 中8维：电流相关特征（均值、标准差、峰度、偏度、CV_Q、CV_time、斜率、熵）
- 第17维：SOH = 当前容量/初始容量

作者：AI Assistant
日期：2025-01-27
"""

import pandas as pd
import numpy as np
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

def calculate_entropy(data):
    """计算数据的熵值"""
    # 将数据归一化到[0,1]区间
    data_norm = (data - data.min()) / (data.max() - data.min() + 1e-8)
    
    # 创建直方图来计算概率分布
    hist, _ = np.histogram(data_norm, bins=50, density=True)
    hist = hist / (hist.sum() + 1e-8)  # 归一化为概率
    
    # 计算熵
    entropy = -np.sum(hist * np.log(hist + 1e-8))
    return entropy

def extract_voltage_features(voltage_data, time_data, current_data):
    """提取电压相关的8维特征"""
    features = []
    
    # 1. 电压均值
    voltage_mean = np.mean(voltage_data)
    features.append(voltage_mean)
    
    # 2. 电压标准差
    voltage_std = np.std(voltage_data, ddof=1)
    features.append(voltage_std)
    
    # 3. 电压峰度
    voltage_kurtosis = stats.kurtosis(voltage_data)
    features.append(voltage_kurtosis)
    
    # 4. 电压偏度
    voltage_skewness = stats.skew(voltage_data)
    features.append(voltage_skewness)
    
    # 5. CC Q (恒流充电的电量) - 使用数值积分
    # 这里假设是充电过程，电流为正值
    if len(time_data) > 1:
        dt = np.diff(time_data)  # 时间间隔
        current_avg = (current_data[:-1] + current_data[1:]) / 2  # 平均电流
        cc_q = np.sum(current_avg * dt)  # 电量积分
    else:
        cc_q = 0
    features.append(abs(cc_q))  # 取绝对值
    
    # 6. CC charge time (恒流充电时间)
    cc_time = time_data[-1] - time_data[0] if len(time_data) > 1 else 0
    features.append(cc_time)
    
    # 7. 电压斜率
    if len(time_data) > 1:
        voltage_slope = (voltage_data[-1] - voltage_data[0]) / (time_data[-1] - time_data[0])
    else:
        voltage_slope = 0
    features.append(voltage_slope)
    
    # 8. 电压熵
    voltage_entropy = calculate_entropy(voltage_data)
    features.append(voltage_entropy)
    
    return features

def extract_current_features(current_data, time_data):
    """提取电流相关的8维特征"""
    features = []
    
    # 1. 电流均值
    current_mean = np.mean(current_data)
    features.append(current_mean)
    
    # 2. 电流标准差
    current_std = np.std(current_data, ddof=1)
    features.append(current_std)
    
    # 3. 电流峰度
    current_kurtosis = stats.kurtosis(current_data)
    features.append(current_kurtosis)
    
    # 4. 电流偏度
    current_skewness = stats.skew(current_data)
    features.append(current_skewness)
    
    # 5. CV Q (恒压充电的电量) - 这里用电流变化来近似
    if len(time_data) > 1:
        dt = np.diff(time_data)
        current_avg = (current_data[:-1] + current_data[1:]) / 2
        cv_q = np.sum(current_avg * dt)
    else:
        cv_q = 0
    features.append(abs(cv_q))
    
    # 6. CV charge time (恒压充电时间) - 这里用总时间
    cv_time = time_data[-1] - time_data[0] if len(time_data) > 1 else 0
    features.append(cv_time)
    
    # 7. 电流斜率
    if len(time_data) > 1:
        current_slope = (current_data[-1] - current_data[0]) / (time_data[-1] - time_data[0])
    else:
        current_slope = 0
    features.append(current_slope)
    
    # 8. 电流熵
    current_entropy = calculate_entropy(current_data)
    features.append(current_entropy)
    
    return features

def calculate_capacity(cycle_data):
    """计算一个cycle的容量"""
    # 找到放电数据（电流为负值）
    discharge_data = cycle_data[cycle_data['工步类型'] == '恒流放电'].copy()
    
    if len(discharge_data) == 0:
        return 0
    
    # 使用比容量的最大值作为该cycle的容量
    # 比容量(mAh/g)列包含了累积的容量信息
    capacity = discharge_data['比容量(mAh/g)'].max()
    
    return capacity

def extract_features_from_csv(csv_path):
    """从CSV文件中提取所有cycle的特征"""
    print(f"正在读取数据文件: {csv_path}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_path, encoding='utf-8')
    print(f"数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    
    # 获取所有的循环号
    cycles = sorted(df['循环号'].unique())
    print(f"发现 {len(cycles)} 个循环: {cycles}")
    
    all_features = []
    all_soh = []
    initial_capacity = None
    
    for cycle_num in cycles:
        print(f"\n处理循环 {cycle_num}...")
        
        # 获取当前循环的所有数据
        cycle_data = df[df['循环号'] == cycle_num].copy()
        
        # 计算当前循环的容量
        current_capacity = calculate_capacity(cycle_data)
        
        # 设置初始容量（第一个循环的容量）
        if initial_capacity is None:
            initial_capacity = current_capacity
            print(f"初始容量: {initial_capacity:.2f} mAh/g")
        
        # 计算SOH
        soh = current_capacity / initial_capacity if initial_capacity > 0 else 0
        print(f"当前容量: {current_capacity:.2f} mAh/g, SOH: {soh:.4f}")
        
        # 分别获取充电和放电数据
        charge_data = cycle_data[cycle_data['工步类型'] == '恒流充电'].copy()
        discharge_data = cycle_data[cycle_data['工步类型'] == '恒流放电'].copy()
        
        # 如果没有充电或放电数据，跳过这个循环
        if len(charge_data) == 0 or len(discharge_data) == 0:
            print(f"循环 {cycle_num} 缺少充电或放电数据，跳过")
            continue
        
        # 合并充放电数据用于特征提取
        combined_data = pd.concat([discharge_data, charge_data]).sort_values('总时间(h)')
        
        # 提取数据
        voltage_data = combined_data['电压(V)'].values
        current_data = combined_data['电流(mA)'].values / 1000  # 转换为A
        time_data = combined_data['总时间(h)'].values
        
        print(f"  电压范围: {voltage_data.min():.3f} - {voltage_data.max():.3f} V")
        print(f"  电流范围: {current_data.min():.3f} - {current_data.max():.3f} A")
        print(f"  时间范围: {time_data.min():.3f} - {time_data.max():.3f} h")
        
        # 提取电压特征（8维）
        voltage_features = extract_voltage_features(voltage_data, time_data, current_data)
        
        # 提取电流特征（8维）
        current_features = extract_current_features(current_data, time_data)
        
        # 合并所有特征
        cycle_features = voltage_features + current_features
        
        print(f"  提取特征维度: {len(cycle_features)}")
        
        all_features.append(cycle_features)
        all_soh.append(soh)
    
    return np.array(all_features), np.array(all_soh), cycles

def save_features(features, soh, cycles, output_path):
    """保存特征到文件"""
    print(f"\n保存特征到: {output_path}")
    
    # 创建特征名称
    voltage_feature_names = [
        'voltage_mean', 'voltage_std', 'voltage_kurtosis', 'voltage_skewness',
        'CC_Q', 'CC_time', 'voltage_slope', 'voltage_entropy'
    ]
    
    current_feature_names = [
        'current_mean', 'current_std', 'current_kurtosis', 'current_skewness', 
        'CV_Q', 'CV_time', 'current_slope', 'current_entropy'
    ]
    
    feature_names = voltage_feature_names + current_feature_names + ['SOH']
    
    # 合并特征和SOH
    data_with_soh = np.column_stack([features, soh])
    
    # 创建DataFrame
    df_features = pd.DataFrame(data_with_soh, columns=feature_names)
    df_features.insert(0, 'cycle', cycles[:len(features)])
    
    # 保存为CSV
    df_features.to_csv(output_path, index=False, encoding='utf-8')
    
    print(f"特征统计:")
    print(f"  样本数量: {len(features)}")
    print(f"  特征维度: {features.shape[1]}")
    print(f"  SOH范围: {soh.min():.4f} - {soh.max():.4f}")
    
    return df_features

def main():
    """主函数"""
    # 设置文件路径
    csv_path = r"E:\gitrepo\PINN4SOH\AHNU_Project\data\cycledata.csv"
    output_dir = r"E:\gitrepo\PINN4SOH\AHNU_Project\data"
    output_path = os.path.join(output_dir, "AHNU_17_features.csv")
    
    # 检查输入文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误: 找不到输入文件 {csv_path}")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 提取特征
        features, soh, cycles = extract_features_from_csv(csv_path)
        
        if len(features) == 0:
            print("错误: 没有提取到任何特征")
            return
        
        # 保存特征
        df_features = save_features(features, soh, cycles, output_path)
        
        # 显示前几行数据
        print(f"\n前5行特征数据:")
        print(df_features.head())
        
        print(f"\n特征提取完成! 输出文件: {output_path}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()