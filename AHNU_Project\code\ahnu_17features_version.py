#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AHNU电池SOH预测17特征版本
从9个原始特征扩展到17个工程特征
数据路径：绝对路径版本
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
import matplotlib.pyplot as plt
import os
import joblib

class AHNU17FeatureProcessor:
    """AHNU数据处理器 - 17特征版本"""
    
    def __init__(self):
        # 使用绝对路径
        self.data_path = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
        self.scaler = StandardScaler()
        self.nominal_capacity = 1200.0  # 额定容量
        
    def load_and_process_data(self):
        """加载数据并提取17维特征"""
        print(f"📊 加载AHNU数据: {self.data_path}")
        
        df = pd.read_excel(self.data_path)
        print(f"原始数据形状: {df.shape}")
        
        # 提取17维特征
        features_17d = self._extract_17_features(df)
        
        # 计算SOH标签
        soh_labels = self._calculate_soh(df)
        
        print(f"📊 17维特征矩阵形状: {features_17d.shape}")
        print(f"📊 SOH范围: [{soh_labels.min():.4f}, {soh_labels.max():.4f}]")
        
        return features_17d, soh_labels
    
    def _extract_17_features(self, df):
        """从AHNU数据提取17维统计特征"""
        features = []
        
        # 选择关键的电池参数列进行特征提取
        key_columns = [
            '充电比容量(mAh/g)', '放电比容量(mAh/g)', '充放电效率(%)',
            '中值电压(V)', '充电时间(h)', '放电时间(h)', 
            '充电平均电压(V)', '放电平均电压(V)'
        ]
        
        print(f"\n🔧 17维特征提取策略:")
        print(f"  1-8维: 原始电池参数")
        print(f"  9-10维: 容量比率特征")  
        print(f"  11-12维: 电压差和时间比特征")
        print(f"  13-14维: 效率变换特征")
        print(f"  15-16维: 循环相关特征")
        print(f"  17维: 综合健康指标")
        
        for _, row in df.iterrows():
            row_features = []
            
            # 1-8: 各列的当前值
            for col in key_columns:
                row_features.append(row[col])
            
            # 9-10: 容量相关比率
            charge_cap = row['充电比容量(mAh/g)']
            discharge_cap = row['放电比容量(mAh/g)']
            row_features.append(charge_cap / self.nominal_capacity)  # 充电容量比
            row_features.append(discharge_cap / self.nominal_capacity)  # 放电容量比
            
            # 11-12: 电压差和时间比
            voltage_diff = row['充电平均电压(V)'] - row['放电平均电压(V)']
            time_ratio = row['充电时间(h)'] / (row['放电时间(h)'] + 1e-6)
            row_features.append(voltage_diff)
            row_features.append(time_ratio)
            
            # 13-14: 效率相关特征
            efficiency = row['充放电效率(%)']
            row_features.append(efficiency / 100.0)  # 标准化效率
            row_features.append(np.log(efficiency + 1))  # 对数变换效率
            
            # 15-16: 循环相关特征
            cycle_num = row['循环号']
            row_features.append(cycle_num / 216.0)  # 标准化循环号
            row_features.append(np.sqrt(cycle_num))  # 平方根变换
            
            # 17: 综合健康指标
            health_indicator = (discharge_cap * efficiency / 100.0) / self.nominal_capacity
            row_features.append(health_indicator)
            
            features.append(row_features)
        
        features_array = np.array(features)
        
        # 标准化特征
        features_array = self.scaler.fit_transform(features_array)
        
        return features_array
    
    def _calculate_soh(self, df):
        """计算SOH值"""
        # 使用放电容量计算SOH
        discharge_capacity = df['放电比容量(mAh/g)'].values
        initial_capacity = discharge_capacity[0]  # 第一个循环的容量作为初始容量
        soh_values = discharge_capacity / initial_capacity
        
        # 确保SOH在合理范围内
        soh_values = np.clip(soh_values, 0.1, 1.2)
        
        return soh_values
    
    def prepare_data_for_training(self, X, y):
        """准备训练数据"""
        # 时间序列划分 - 前80%训练，后20%测试
        split_idx = int(len(X) * 0.8)
        
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        print(f"📊 数据划分: 训练集={len(X_train)}, 测试集={len(X_test)}")
        
        return X_train, X_test, y_train, y_test

class AHNU17FeatureModel:
    """AHNU 17特征模型"""
    
    def __init__(self):
        self.models = {}
        self.best_model_name = None
        self.best_model = None
        
    def train_all_models(self, X_train, y_train, X_test, y_test):
        """训练所有候选模型"""
        
        print(f"\n🏋️  训练模型集合 (特征维度: {X_train.shape[1]})")
        print("="*50)
        
        results = {}
        
        # 1. 线性回归
        print("📊 1. 线性回归")
        lr = LinearRegression()
        lr.fit(X_train, y_train)
        y_pred_lr = lr.predict(X_test)
        
        mse_lr = np.mean((y_test - y_pred_lr) ** 2)
        mae_lr = np.mean(np.abs(y_test - y_pred_lr))
        r2_lr = 1 - np.sum((y_test - y_pred_lr) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
        
        results['Linear Regression'] = {'model': lr, 'pred': y_pred_lr, 'MSE': mse_lr, 'MAE': mae_lr, 'R2': r2_lr}
        self.models['Linear Regression'] = lr
        
        print(f"  MSE: {mse_lr:.8f}, MAE: {mae_lr:.6f}, R²: {r2_lr:.6f}")
        
        # 2. 随机森林
        print("🌲 2. 随机森林")
        rf = RandomForestRegressor(
            n_estimators=100,
            max_depth=8,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42
        )
        rf.fit(X_train, y_train)
        y_pred_rf = rf.predict(X_test)
        
        mse_rf = np.mean((y_test - y_pred_rf) ** 2)
        mae_rf = np.mean(np.abs(y_test - y_pred_rf))
        r2_rf = 1 - np.sum((y_test - y_pred_rf) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
        
        results['Random Forest'] = {'model': rf, 'pred': y_pred_rf, 'MSE': mse_rf, 'MAE': mae_rf, 'R2': r2_rf}
        self.models['Random Forest'] = rf
        
        print(f"  MSE: {mse_rf:.8f}, MAE: {mae_rf:.6f}, R²: {r2_rf:.6f}")
        
        # 3. 正则化线性回归 (Ridge)
        print("📈 3. 岭回归 (Ridge)")
        from sklearn.linear_model import Ridge
        
        ridge = Ridge(alpha=1.0)
        ridge.fit(X_train, y_train)
        y_pred_ridge = ridge.predict(X_test)
        
        mse_ridge = np.mean((y_test - y_pred_ridge) ** 2)
        mae_ridge = np.mean(np.abs(y_test - y_pred_ridge))
        r2_ridge = 1 - np.sum((y_test - y_pred_ridge) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
        
        results['Ridge Regression'] = {'model': ridge, 'pred': y_pred_ridge, 'MSE': mse_ridge, 'MAE': mae_ridge, 'R2': r2_ridge}
        self.models['Ridge Regression'] = ridge
        
        print(f"  MSE: {mse_ridge:.8f}, MAE: {mae_ridge:.6f}, R²: {r2_ridge:.6f}")
        
        # 选择最佳模型
        best_r2 = max(results[name]['R2'] for name in results)
        for name, metrics in results.items():
            if metrics['R2'] == best_r2:
                self.best_model_name = name
                self.best_model = self.models[name]
                break
        
        print(f"\n🏆 最佳模型: {self.best_model_name} (R² = {best_r2:.6f})")
        
        return results
    
    def analyze_feature_importance(self):
        """分析17维特征重要性"""
        
        feature_names = [
            '充电比容量', '放电比容量', '充放电效率', '中值电压', 
            '充电时间', '放电时间', '充电平均电压', '放电平均电压',
            '充电容量比', '放电容量比', '电压差', '时间比',
            '标准化效率', '对数效率', '标准化循环号', '平方根循环号',
            '综合健康指标'
        ]
        
        if self.best_model_name == 'Linear Regression':
            coef = self.best_model.coef_
            importance = abs(coef) / sum(abs(coef))
        elif self.best_model_name == 'Random Forest':
            importance = self.best_model.feature_importances_
        elif self.best_model_name == 'Ridge Regression':
            coef = self.best_model.coef_
            importance = abs(coef) / sum(abs(coef))
        else:
            importance = np.ones(17) / 17
        
        print(f"\n📊 {self.best_model_name} 17维特征重要性:")
        print("-"*50)
        
        # 排序并显示
        feature_importance = list(zip(feature_names, importance))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        for i, (name, imp) in enumerate(feature_importance, 1):
            print(f"  {i:2d}. {name:<15}: {imp:.4f}")
        
        return dict(feature_importance)

def plot_17feature_results(results, y_test, save_dir):
    """绘制17特征结果"""
    
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 找到最佳模型
    best_r2 = max(results[name]['R2'] for name in results)
    best_name = None
    for name, metrics in results.items():
        if metrics['R2'] == best_r2:
            best_name = name
            break
    
    # 1. 最佳模型预测对比
    ax = axes[0, 0]
    cycles = range(len(y_test))
    y_pred_best = results[best_name]['pred']
    
    ax.plot(cycles, y_test, label='True SOH', color='black', linewidth=2, marker='o', markersize=3)
    ax.plot(cycles, y_pred_best, label=f'{best_name}', color='red', linewidth=2, marker='s', markersize=3)
    ax.set_xlabel('Test Sample Index')
    ax.set_ylabel('SOH')
    ax.set_title(f'17-Feature Model: {best_name} (R²={best_r2:.4f})')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. 误差分析
    ax = axes[0, 1]
    errors = np.abs(y_test - y_pred_best)
    ax.plot(cycles, errors, color='green', linewidth=2, marker='d', markersize=3)
    ax.set_xlabel('Test Sample Index')
    ax.set_ylabel('Absolute Error')
    ax.set_title(f'Prediction Error (MAE: {results[best_name]["MAE"]:.6f})')
    ax.grid(True, alpha=0.3)
    
    # 3. 散点图
    ax = axes[1, 0]
    ax.scatter(y_test, y_pred_best, alpha=0.7, s=50, color='blue')
    min_val, max_val = min(y_test.min(), y_pred_best.min()), max(y_test.max(), y_pred_best.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.set_title('True vs Predicted (17 Features)')
    ax.grid(True, alpha=0.3)
    
    # 4. 所有模型对比
    ax = axes[1, 1]
    model_names = list(results.keys())
    r2_scores = [results[name]['R2'] for name in model_names]
    
    colors = ['skyblue', 'lightgreen', 'lightcoral']
    bars = ax.bar(model_names, r2_scores, color=colors)
    ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax.set_ylabel('R² Score')
    ax.set_title('17-Feature Models Comparison')
    ax.tick_params(axis='x', rotation=45)
    
    for bar, score in zip(bars, r2_scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    # 保存
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, 'ahnu_17features_results.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"📊 17特征结果图已保存: {save_path}")

def main():
    """主函数"""
    
    print("🚀 AHNU电池SOH预测17特征版本")
    print("="*70)
    
    # 输出路径设置
    results_dir = r'E:\gitrepo\PINN4SOH\AHNU_Project\results'
    plots_dir = r'E:\gitrepo\PINN4SOH\AHNU_Project\plots'
    
    # 1. 数据处理
    processor = AHNU17FeatureProcessor()
    X, y = processor.load_and_process_data()
    X_train, X_test, y_train, y_test = processor.prepare_data_for_training(X, y)
    
    # 2. 模型训练
    model = AHNU17FeatureModel()
    results = model.train_all_models(X_train, y_train, X_test, y_test)
    
    # 3. 特征重要性分析
    importance = model.analyze_feature_importance()
    
    # 4. 结果可视化
    plot_17feature_results(results, y_test, plots_dir)
    
    # 5. 保存模型和结果
    os.makedirs(results_dir, exist_ok=True)
    
    best_model_path = os.path.join(results_dir, 'best_ahnu_17features_model.pkl')
    scaler_path = os.path.join(results_dir, 'feature_scaler_17features.pkl')
    
    joblib.dump(model.best_model, best_model_path)
    joblib.dump(processor.scaler, scaler_path)
    
    # 保存预测结果
    best_pred = results[model.best_model_name]['pred']
    np.save(os.path.join(results_dir, 'optimal_true_labels_17features.npy'), y_test)
    np.save(os.path.join(results_dir, 'optimal_predictions_17features.npy'), best_pred)
    
    print(f"\n📁 模型和结果已保存到: {results_dir}")
    print(f"📊 图表已保存到: {plots_dir}")
    
    # 6. 总结报告
    print(f"\n📋 AHNU 17特征版本总结")
    print("="*70)
    
    best_metrics = results[model.best_model_name]
    print(f"🏆 最佳模型: {model.best_model_name}")
    print(f"📊 特征数量: 17 个工程特征")
    print(f"📊 性能指标:")
    print(f"  - MSE: {best_metrics['MSE']:.8f}")
    print(f"  - MAE: {best_metrics['MAE']:.6f}")
    print(f"  - R²:  {best_metrics['R2']:.6f}")
    
    print(f"\n✨ 答案: 当前使用 **17 个特征** 预测SOH")
    
    return results

if __name__ == "__main__":
    results = main()