"""
将AHNU_17_features.csv转换为与原始Model兼容的格式
Convert AHNU_17_features.csv to format compatible with original Model
"""
import pandas as pd
import numpy as np
import os

def preprocess_ahnu_data():
    """
    预处理AHNU数据以匹配原始Model的期望格式
    
    原始Model期望格式:
    - 16个特征列 (voltage mean, voltage std, ..., current entropy)
    - 1个capacity列 (用于计算SOH)
    - cycle index会在dataloader中动态添加
    
    AHNU数据格式:
    - cycle列 (需要移除，因为会在dataloader中重新添加)
    - 16个特征列 (与原始Model相同)
    - SOH列 (需要转换为capacity格式)
    """
    
    print("🔄 开始预处理AHNU数据...")
    
    # 读取AHNU数据
    input_file = './data/AHNU_17_features.csv'
    output_file = './data/AHNU_for_original_model.csv'
    
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"找不到输入文件: {input_file}")
    
    df = pd.read_csv(input_file)
    print(f"📊 原始数据形状: {df.shape}")
    print(f"📋 原始列名: {list(df.columns)}")
    
    # 1. 移除cycle列（因为原始Model会在dataloader中重新添加cycle index）
    if 'cycle' in df.columns:
        df = df.drop('cycle', axis=1)
        print("✅ 已移除cycle列")
    
    # 2. 重命名SOH列为capacity（原始Model期望的列名）
    if 'SOH' in df.columns:
        df = df.rename(columns={'SOH': 'capacity'})
        print("✅ 已将SOH列重命名为capacity")
    
    # 3. 验证特征列数量
    feature_cols = [col for col in df.columns if col != 'capacity']
    print(f"📈 特征列数量: {len(feature_cols)}")
    print(f"📋 特征列名: {feature_cols}")
    
    if len(feature_cols) != 16:
        print(f"⚠️  警告: 特征列数量为{len(feature_cols)}，期望16列")
        if len(feature_cols) > 16:
            print("🔧 保留前16个特征列")
            feature_cols = feature_cols[:16]
            df = df[feature_cols + ['capacity']]
    
    # 4. 检查数据范围
    print(f"\n📊 数据统计:")
    print(f"   - capacity范围: [{df['capacity'].min():.4f}, {df['capacity'].max():.4f}]")
    print(f"   - capacity均值: {df['capacity'].mean():.4f}")
    print(f"   - 数据行数: {len(df)}")
    
    # 5. 保存处理后的数据
    df.to_csv(output_file, index=False)
    print(f"✅ 预处理完成! 数据已保存到: {output_file}")
    print(f"📊 最终数据形状: {df.shape}")
    print(f"📋 最终列名: {list(df.columns)}")
    
    return output_file

def verify_format_compatibility():
    """验证格式兼容性"""
    print(f"\n🔍 验证格式兼容性...")
    
    # 检查处理后的文件
    processed_file = './data/AHNU_for_original_model.csv'
    if not os.path.exists(processed_file):
        print("❌ 处理后的文件不存在")
        return False
    
    df = pd.read_csv(processed_file)
    
    # 验证列数
    expected_cols = 17  # 16特征 + 1capacity
    if df.shape[1] != expected_cols:
        print(f"❌ 列数不匹配: 期望{expected_cols}列，实际{df.shape[1]}列")
        return False
    
    # 验证capacity列存在
    if 'capacity' not in df.columns:
        print("❌ 缺少capacity列")
        return False
    
    # 验证特征列数量
    feature_cols = [col for col in df.columns if col != 'capacity']
    if len(feature_cols) != 16:
        print(f"❌ 特征列数量不匹配: 期望16列，实际{len(feature_cols)}列")
        return False
    
    print("✅ 格式验证通过!")
    print(f"   - 总列数: {df.shape[1]} (16特征 + 1capacity)")
    print(f"   - 数据行数: {df.shape[0]}")
    print(f"   - capacity范围: [{df['capacity'].min():.4f}, {df['capacity'].max():.4f}]")
    
    return True

def create_sample_comparison():
    """创建样本对比，展示转换前后的差异"""
    print(f"\n📋 创建样本对比...")
    
    # 原始数据
    original_df = pd.read_csv('./data/AHNU_17_features.csv')
    processed_df = pd.read_csv('./data/AHNU_for_original_model.csv')
    
    print("🔍 转换前后对比 (前5行):")
    print("\n原始数据:")
    print(original_df.head())
    print(f"形状: {original_df.shape}")
    
    print("\n处理后数据:")
    print(processed_df.head())
    print(f"形状: {processed_df.shape}")
    
    print("\n📊 关键变化:")
    print(f"   - 移除了cycle列")
    print(f"   - SOH列重命名为capacity列")
    print(f"   - 保持16个特征列不变")
    print(f"   - cycle index将在dataloader中动态添加")

if __name__ == "__main__":
    print("🚀 AHNU数据预处理 - 适配原始Model格式")
    print("=" * 60)
    
    try:
        # 1. 预处理数据
        output_file = preprocess_ahnu_data()
        
        # 2. 验证格式
        if verify_format_compatibility():
            print("\n🎉 数据预处理成功完成!")
        else:
            print("\n❌ 格式验证失败")
            
        # 3. 创建对比
        create_sample_comparison()
        
        print(f"\n📁 输出文件: {output_file}")
        print("🔄 现在可以使用原始Model代码进行训练了!")
        
    except Exception as e:
        print(f"❌ 预处理失败: {e}")
        import traceback
        traceback.print_exc()
