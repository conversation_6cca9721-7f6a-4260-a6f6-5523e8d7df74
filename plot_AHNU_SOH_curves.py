import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib import rcParams
from scipy import stats

# Set Arial font
rcParams['font.family'] = 'Arial'
rcParams['font.size'] = 12

def extract_soh_from_csv(csv_path, battery_name):
    """
    从单个CSV文件提取SOH数据
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_path, encoding='utf-8')
        
        # 获取所有循环号
        cycles = sorted(df['循环号'].unique())
        
        # 计算每个循环的容量和SOH
        cycle_capacities = []
        cycle_numbers = []
        
        for cycle in cycles:
            cycle_data = df[df['循环号'] == cycle]
            
            # 找到放电数据
            discharge_data = cycle_data[cycle_data['工步类型'] == '恒流放电']
            
            if len(discharge_data) > 0:
                # 获取该循环的最大比容量
                max_capacity = discharge_data['比容量(mAh/g)'].max()
                cycle_capacities.append(max_capacity)
                cycle_numbers.append(cycle)
        
        # 转换为numpy数组
        cycle_numbers = np.array(cycle_numbers)
        cycle_capacities = np.array(cycle_capacities)
        
        # 计算SOH (以第一个循环的容量为基准)
        if len(cycle_capacities) > 0:
            initial_capacity = cycle_capacities[0]
            soh = cycle_capacities / initial_capacity
        else:
            soh = np.array([])
        
        print(f"{battery_name}: {len(cycle_numbers)} cycles, "
              f"Capacity range: {cycle_capacities.min():.2f} - {cycle_capacities.max():.2f} mAh/g, "
              f"SOH range: {soh.min():.3f} - {soh.max():.3f}")
        
        return cycle_numbers, soh, cycle_capacities
        
    except Exception as e:
        print(f"Error processing {csv_path}: {e}")
        return np.array([]), np.array([]), np.array([])

def plot_AHNU_SOH_curves():
    """
    绘制AHNU电池的SOH vs Cycles曲线
    """
    # 数据目录
    data_dir = 'AHNU_Project/data/电池数据-0.01-3V'
    
    # 获取所有CSV文件
    csv_files = []
    for i in range(1, 6):  # Si@C-1.<NAME_EMAIL>
        file_path = os.path.join(data_dir, f'Si@C-{i}.csv')
        if os.path.exists(file_path):
            csv_files.append((file_path, f'Si@C-{i}'))
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 颜色设置
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    all_soh_data = []
    battery_stats = []
    
    print("="*60)
    print("AHNU BATTERY SOH ANALYSIS")
    print("="*60)
    
    # 处理每个电池文件
    for idx, (file_path, battery_name) in enumerate(csv_files):
        cycles, soh, capacities = extract_soh_from_csv(file_path, battery_name)
        
        if len(cycles) > 0:
            # 存储数据用于统计
            all_soh_data.append(soh)
            battery_stats.append({
                'battery': battery_name,
                'cycles': len(cycles),
                'initial_soh': soh[0] if len(soh) > 0 else 0,
                'final_soh': soh[-1] if len(soh) > 0 else 0,
                'initial_capacity': capacities[0] if len(capacities) > 0 else 0,
                'final_capacity': capacities[-1] if len(capacities) > 0 else 0,
                'degradation': (soh[0] - soh[-1]) if len(soh) > 0 else 0,
                'degradation_rate': ((soh[0] - soh[-1]) / len(cycles) * 100) if len(cycles) > 0 else 0
            })
            
            # 绘制SOH曲线
            ax1.plot(cycles, soh, 
                    color=colors[idx % len(colors)], 
                    linewidth=2.5, 
                    label=battery_name,
                    marker='o',
                    markersize=4,
                    alpha=0.8)
    
    # 自定义第一个子图
    ax1.set_xlabel('Cycle Number', fontsize=14, fontweight='bold')
    ax1.set_ylabel('State of Health (SOH)', fontsize=14, fontweight='bold')
    ax1.set_title('SOH Degradation Curves for AHNU Si@C Batteries', fontsize=16, fontweight='bold')
    ax1.grid(True, alpha=0.3, linestyle='--')
    ax1.legend(frameon=True, fancybox=True, shadow=True)
    ax1.set_ylim(0, 1.1)
    
    # 添加80%和90% SOH阈值线
    ax1.axhline(y=0.8, color='red', linestyle='--', linewidth=2, alpha=0.7, label='80% SOH Threshold')
    ax1.axhline(y=0.9, color='orange', linestyle='--', linewidth=2, alpha=0.7, label='90% SOH Threshold')
    
    # 绘制容量对比图
    if battery_stats:
        battery_names = [stat['battery'] for stat in battery_stats]
        initial_capacities = [stat['initial_capacity'] for stat in battery_stats]
        final_capacities = [stat['final_capacity'] for stat in battery_stats]
        
        x = np.arange(len(battery_names))
        width = 0.35
        
        bars1 = ax2.bar(x - width/2, initial_capacities, width, 
                       label='Initial Capacity', color='lightblue', alpha=0.8, edgecolor='black')
        bars2 = ax2.bar(x + width/2, final_capacities, width,
                       label='Final Capacity', color='lightcoral', alpha=0.8, edgecolor='black')
        
        # 添加数值标签
        for bar, value in zip(bars1, initial_capacities):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                    f'{value:.0f}', ha='center', va='bottom', fontweight='bold')
        
        for bar, value in zip(bars2, final_capacities):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 5,
                    f'{value:.0f}', ha='center', va='bottom', fontweight='bold')
        
        ax2.set_xlabel('Battery', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Capacity (mAh/g)', fontsize=14, fontweight='bold')
        ax2.set_title('Initial vs Final Capacity Comparison', fontsize=16, fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels(battery_names)
        ax2.legend()
        ax2.grid(True, alpha=0.3, linestyle='--', axis='y')
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('AHNU_SOH_curves.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig('AHNU_SOH_curves.pdf', bbox_inches='tight', facecolor='white')
    
    # 打印统计信息
    print("\nBATTERY STATISTICS:")
    print("-"*80)
    print(f"{'Battery':<10} {'Cycles':<8} {'Init Cap':<10} {'Final Cap':<11} {'Init SOH':<10} {'Final SOH':<11} {'Degradation':<12}")
    print("-"*80)
    
    for stat in battery_stats:
        print(f"{stat['battery']:<10} {stat['cycles']:<8} {stat['initial_capacity']:<10.1f} "
              f"{stat['final_capacity']:<11.1f} {stat['initial_soh']:<10.3f} "
              f"{stat['final_soh']:<11.3f} {stat['degradation']:<12.3f}")
    
    if battery_stats:
        avg_cycles = np.mean([stat['cycles'] for stat in battery_stats])
        avg_degradation = np.mean([stat['degradation'] for stat in battery_stats])
        avg_init_cap = np.mean([stat['initial_capacity'] for stat in battery_stats])
        avg_final_cap = np.mean([stat['final_capacity'] for stat in battery_stats])
        
        print("-"*80)
        print(f"{'Average':<10} {avg_cycles:<8.0f} {avg_init_cap:<10.1f} "
              f"{avg_final_cap:<11.1f} {'-':<10} {'-':<11} {avg_degradation:<12.3f}")
    
    print("="*80)
    
    plt.show()
    print(f"\nPlots saved as 'AHNU_SOH_curves.png' and 'AHNU_SOH_curves.pdf'")

def compare_with_extracted_features():
    """
    与已提取的特征数据进行对比
    """
    print(f"\n" + "="*60)
    print("COMPARISON WITH EXTRACTED FEATURES")
    print("="*60)
    
    try:
        # 读取已提取的特征数据
        features_df = pd.read_csv('AHNU_Project/data/AHNU_17_features.csv')
        
        print(f"Extracted features data:")
        print(f"- Total cycles: {len(features_df)}")
        print(f"- SOH range: {features_df['SOH'].min():.4f} - {features_df['SOH'].max():.4f}")
        print(f"- Initial SOH: {features_df['SOH'].iloc[0]:.4f}")
        print(f"- Final SOH: {features_df['SOH'].iloc[-1]:.4f}")
        print(f"- Total degradation: {features_df['SOH'].iloc[0] - features_df['SOH'].iloc[-1]:.4f}")
        
        # 创建对比图
        plt.figure(figsize=(12, 6))
        
        plt.plot(features_df['cycle'], features_df['SOH'], 
                'b-', linewidth=3, label='Extracted Features SOH', alpha=0.8)
        
        plt.xlabel('Cycle Number', fontsize=14, fontweight='bold')
        plt.ylabel('State of Health (SOH)', fontsize=14, fontweight='bold')
        plt.title('AHNU Battery SOH from Extracted Features', fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3, linestyle='--')
        plt.legend(frameon=True, fancybox=True, shadow=True)
        
        # 添加阈值线
        plt.axhline(y=0.8, color='red', linestyle='--', linewidth=2, alpha=0.7, label='80% SOH')
        plt.axhline(y=0.9, color='orange', linestyle='--', linewidth=2, alpha=0.7, label='90% SOH')
        
        plt.tight_layout()
        plt.savefig('AHNU_extracted_features_SOH.png', dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
        print(f"Extracted features SOH plot saved as 'AHNU_extracted_features_SOH.png'")
        
    except Exception as e:
        print(f"Error reading extracted features: {e}")

if __name__ == "__main__":
    # 绘制从原始CSV提取的SOH曲线
    plot_AHNU_SOH_curves()
    
    # 对比已提取的特征数据
    compare_with_extracted_features()