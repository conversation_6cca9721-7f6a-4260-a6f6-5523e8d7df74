# PINN4SOH: 电池健康状态预测的物理信息神经网络项目

> **项目目标**: 基于物理信息神经网络(PINN)的锂离子电池健康状态(SOH)预测与迁移学习框架

[![DOI](https://zenodo.org/badge/536661180.svg)](https://zenodo.org/doi/10.5281/zenodo.11130555)
[![arXiv](http://img.shields.io/badge/arXiv-2204.09810-B31B1B.svg)](https://arxiv.org/abs/2304.07599)

## 目录
* [项目概述](#项目概述)
* [PINN迁移学习策略](#pinn迁移学习策略)
* [项目结构与文件夹功能](#项目结构与文件夹功能)
* [Latent DeepONet方法](#latent-deeponet方法)
* [技术栈与环境](#技术栈与环境)
* [使用说明](#使用说明)
* [引用](#引用)

## 项目概述

本项目是论文[《Physics-informed neural network for lithium-ion battery degradation stable modeling and prognosis》](https://www.nature.com/articles/s41467-024-48779-z)的完整代码实现，专注于锂离子电池健康状态(SOH)预测的物理信息神经网络研究。

### 🎯 核心功能
- **PINN建模**: 基于经验性偏微分方程的电池退化建模
- **迁移学习**: 跨数据集、跨电池类型的知识迁移
- **多数据集支持**: XJTU、TJU、HUST、MIT四个大规模电池数据集
- **对比分析**: 与传统方法(MLP、CNN)的性能对比
- **Latent DeepONet**: 潜在空间中的深度算子网络实现

### 🔬 技术特色
- **物理约束**: 通过PDE损失保证预测的物理合理性
- **双网络架构**: 解网络F(·)与动力学网络G(·)的协同学习
- **小样本学习**: 目标域仅需1个电池数据即可实现有效迁移
- **多化学体系**: 支持NCM、LFP等不同锂电池化学体系

## PINN迁移学习策略

### 📊 核心理论基础

**PINN双网络架构**:
- **解网络 F(·)**: 学习从特征(t,x)到SOH值u的映射关系
- **动力学网络 G(·)**: 学习电池退化的物理动力学规律 ∂u/∂t = G(t,x,u,...)

**关键创新**: 通过损失函数强制两网络保持一致性: L_PDE = |∂F/∂t - G|²

### 🔄 迁移学习策略对比

| 场景 | 解网络F(·) | 动力学网络G(·) | 训练策略 |
|------|------------|----------------|----------|
| **标准训练** | ✅ 参与训练 | ✅ 参与训练 | 两网络联合优化 |
| **迁移学习** | ✅ 微调更新 | ❄️ 冻结权重 | 保持通用动力学，适配特定场景 |

### 🎯 迁移学习原理

**核心假设**: 
- **动力学网络G(·)**: 学习到**通用的电池退化物理规律**，独立于具体充放电协议
- **解网络F(·)**: 学习**特定的特征映射关系**，与数据集和测量条件相关

**实现策略**:
```python
# 1. 加载源域预训练模型
model.load_model(pretrain_model_path)

# 2. 冻结动力学网络
for param in model.dynamical_F.parameters():
    param.requires_grad = False

# 3. 仅微调解网络
optimizer = Adam(model.solution_u.parameters(), lr=adaptation_lr)
```

**物理意义**: 这种策略成功实现了"是什么"(具体SOH值)与"怎么变"(退化规律)的解耦。

<!---
## Application

Three illustrative examples are provided. The first considers a material fracture problem. The second corresponds to the Rayleigh-Benard convective flow where a thin fluid layer is heated from below and instability occurs due to temperature gradient. Finally, the third example considers the spherical shallow-water equations which model large scale atmospheric flows. The codes provided can be implemented to any dataset generated by a time-dependent PDE, however here they demonstrate the method for the shallow-water equation. 
 
<img src="applications.png" width="900">
--->

## 项目结构与文件夹功能

### 📁 根目录文件说明

| 文件 | 功能 | 重要性 |
|------|------|--------|
| `main_XJTU.py` | XJTU数据集标准PINN训练 | ⭐⭐⭐ |
| `main_adaptation - fine-tuning.py` | **PINN迁移学习核心实现** | ⭐⭐⭐⭐⭐ |
| `main_comparision.py` | 对比模型(MLP/CNN)训练 | ⭐⭐⭐ |
| `main_TJU.py`, `main_HUST.py`, `main_MIT.py` | 其他数据集训练脚本 | ⭐⭐ |
| `count parameters.py` | 模型参数统计工具 | ⭐ |

### 🏗️ 核心模块架构

| 文件夹 | 主要功能 | 技术特点 | 创建者 |
|--------|----------|----------|--------|
| **`Model/`** | PINN核心模型定义 | 双网络架构、物理约束 | 原项目 |
| **`dataloader/`** | 多数据集加载器 | 标准化特征提取 | 原项目 |
| **`utils/`** | 工具函数 | 评估指标、日志管理 | 原项目 |
| **`pretrained model/`** | 预训练权重 | 11个预训练模型 | 原项目 |
| **`latent-deeponet/`** | Latent DeepONet实现 | 潜在空间算子学习 | 🆕用户创建 |
| **`neural_operator_experiment/`** | 神经算子实验 | DeepONet-BNN架构 | 🆕用户创建 |
| **`AHNU_Project/`** | AHNU数据集专项 | 小数据集优化 | 🆕用户创建 |

### 📊 数据集与结果

| 文件夹 | 内容 | 规模 |
|--------|------|------|
| **`data/`** | 预处理电池数据 | 4个数据集，55块电池 |
| **`results/`** | 训练结果 | 模型权重、预测结果 |
| **`results analysis/`** | 结果分析脚本 | Excel报告生成 |
| **`plotter/`** | 可视化代码 | 论文图表生成 |

### 🔬 实验代码分类

**✅ 推荐使用 (主目录)**:
- `main_XJTU.py`: 标准PINN训练
- `main_adaptation - fine-tuning.py`: 迁移学习
- `Model/Model.py`: 核心PINN架构

**🧪 实验探索 (用户创建)**:
- `neural_operator_experiment/`: DeepONet + BNN不确定性量化
- `AHNU_Project/`: 小数据集专项优化
- `latent-deeponet/`: 潜在空间算子方法

## Latent DeepONet方法

本文件夹包含的Latent DeepONet实现 ([原始论文](https://arxiv.org/abs/2304.07599)) :

### 核心组件
* `AE.py` - 自编码器类与训练代码
* `DON.py` - Latent DeepONet类与训练代码  
* `plot.py` - 对比结果可视化
* `main.py` - 多方法、多潜在维度结果生成

### 支持的自编码器
1. **Autoencoder** (vanilla-AE)
2. **Multi-Layer Autoencoder** (MLAE) 
3. **Convolutional Autoencoder** (CAE)
4. **Wasserstein Autoencoder** (WAE)

<p align="center">
    <img width="800" src="schematic.png" alt="Latent DeepONet method">
</p>

## 技术栈与环境

### 🔧 核心依赖
- **Python**: 3.7.10
- **PyTorch**: 1.7.1 (深度学习框架)
- **scikit-learn**: 0.24.2 (机器学习)
- **NumPy**: 1.20.3, **Pandas**: 1.3.5 (数据处理)
- **Matplotlib**: 3.3.4, **scienceplots**: (可视化)

### 🚀 环境搭建
```bash
# 1. 创建虚拟环境
conda create -n pinn4soh python=3.7.10
conda activate pinn4soh

# 2. 安装依赖
conda install pytorch=1.7.1
conda install scikit-learn=0.24.2 numpy=1.20.3 pandas=1.3.5 matplotlib=3.3.4
pip install scienceplots
```

### 📊 数据集信息

| 数据集 | 化学体系 | 电池数量 | 特点 | 获取链接 |
|--------|----------|----------|------|----------|
| **XJTU** | NCM | 55块 | 多充放电协议 | [链接](https://wang-fujin.github.io/) |
| **TJU** | NCM | - | 不同温度 | [链接](https://zenodo.org/record/6405084) |
| **HUST** | NCM | - | 恒定工况 | [链接](https://data.mendeley.com/datasets/nsc7hnsg4s/2) |
| **MIT** | LFP | - | 恒定工况 | [链接](https://data.matr.io/1/projects/5c48dd2bc625d700019f3204) |

### 🔬 预训练模型

项目提供11个预训练模型权重:
```
pretrained model/
├── model_XJTU_0.pth ~ model_XJTU_5.pth  # XJTU 6个批次
├── model_TJU_0.pth ~ model_TJU_2.pth    # TJU 3个批次  
├── model_HUST.pth, model_MIT.pth        # HUST, MIT数据集
└── mu_var_pi_*.npz                      # 归一化参数
```

## 使用说明

### 🎯 快速开始

**1. 标准PINN训练 (XJTU数据集)**
```bash
python main_XJTU.py
```

**2. 迁移学习示例 (TJU→XJTU)**
```bash
# 训练源域模型 (如果没有预训练权重)
python main_TJU.py --batch 2

# 执行迁移学习
python "main_adaptation - fine-tuning.py" --source TJU --target XJTU --source_batch 2 --target_batch 0
```

**3. 模型对比 (MLP/CNN基线)**
```bash
python main_comparision.py --model MLP
python main_comparision.py --model CNN
```

### 🔬 Latent DeepONet训练

针对高维PDE问题的潜在空间算子学习:

```bash
# 1. 训练自编码器
python latent-deeponet/AE.py --method MLAE --latent_dim 16 --n_epochs 1000

# 2. 训练Latent DeepONet
python latent-deeponet/DON.py --method MLAE --latent_dim 16 --n_epochs 1000

# 3. 生成对比图表
python latent-deeponet/plot.py
```

### 📊 关键参数配置

**PINN训练超参数**:
```python
epochs = 200          # 训练轮数
batch_size = 256      # 批次大小  
lr = 1e-2            # 学习率
alpha = 0.7          # PDE损失权重
beta = 0.2           # 物理约束权重
```

**迁移学习超参数**:
```python
adaptation_lr = 4e-4     # 微调学习率
adaptation_epochs = 200  # 微调轮数
small_sample = 1         # 目标域电池数量
```

### 🎯 支持的迁移学习任务

| 源域 | 目标域 | 学习率 | 物理意义 |
|------|--------|--------|----------|
| TJU → XJTU | 各批次 | 0.0004-0.01 | 跨充放电协议 |
| XJTU → TJU | 各批次 | 0.002-0.003 | 跨数据集迁移 |
| HUST → MIT | 单一 | 0.005 | 跨化学体系(NCM→LFP) |
| MIT → HUST | 单一 | 0.0002 | 跨化学体系(LFP→NCM) |

### 📈 结果分析

```bash
# 生成Excel结果报告
python "results analysis/XJTU results.py"
python "results analysis/Comparision results.py"

# 生成论文图表
python plotter/plot_*.py
``` 


## 引用

### 🏆 主要论文

**PINN4SOH (本项目核心方法)**:
```bibtex
@article{wang2024physics,
  title={Physics-informed neural network for lithium-ion battery degradation stable modeling and prognosis},
  author={Wang, Fujin and Zhai, Zhi and Zhao, Zhibin and Di, Yi and Chen, Xuefeng},
  journal={Nature Communications},
  volume={15},
  number={1},
  pages={4332},
  year={2024},
  publisher={Nature Publishing Group UK London}
}
```

**Latent DeepONet (算子学习方法)**:
```bibtex
@article{kontolati2024learning,
  title={Learning nonlinear operators in latent spaces for real-time predictions of complex dynamics in physical systems},
  author={Kontolati, Katiana and Goswami, Somdatta and Em Karniadakis, George and Shields, Michael D},
  journal={Nature Communications},
  volume={15},
  number={1},
  pages={5101},
  year={2024},
  publisher={Nature Publishing Group UK London}
}
```

### 👥 维护者

**原始PINN4SOH项目**: [Wang Fujin](https://wang-fujin.github.io/)  
**Latent DeepONet**: [Katiana Kontolati](https://katiana22.github.io/) :email: <EMAIL>

---

> **项目地位**: 本项目成功将物理信息神经网络与迁移学习结合，为电池健康管理领域提供了**物理一致性**与**跨域泛化能力**并重的解决方案。通过"冻结动力学，微调映射"的创新策略，实现了电池退化通用规律与特定应用场景的有效解耦。



