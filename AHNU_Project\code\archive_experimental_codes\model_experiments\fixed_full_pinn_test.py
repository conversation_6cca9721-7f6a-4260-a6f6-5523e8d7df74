#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版AHNU数据集完整PINN模型测试
使用 solution_u + dynamical_F + 物理约束
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import sys
import os
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from Model.Model import PINN as BasePINN
from utils.util import eval_metrix

device = 'cuda' if torch.cuda.is_available() else 'cpu'

class PINN(BasePINN):
    """修复的PINN模型，正确处理维度和梯度计算"""
    
    def forward(self, xt):
        xt.requires_grad = True
        x = xt[:, 0:-1]  # 前17维特征
        t = xt[:, -1:]   # 最后1维时间
        
        # Solution_u只接受17维特征输入
        u = self.solution_u(x)
        
        # 计算梯度
        u_t = torch.autograd.grad(u.sum(), t, create_graph=True, only_inputs=True, allow_unused=True)[0]
        u_x = torch.autograd.grad(u.sum(), x, create_graph=True, only_inputs=True, allow_unused=True)[0]
        
        # 如果梯度为None，创建零张量
        if u_t is None:
            u_t = torch.zeros_like(t)
        if u_x is None:
            u_x = torch.zeros_like(x)
        
        # 修复dynamical_F输入维度问题
        # dynamical_F期望35维输入，实际应该是：xt(18) + u(1) + u_x(17) + u_t(1) = 37维
        # 但根据Model.py中定义，dynamical_F期望输入维度是35，所以我们需要调整
        # 正确的拼接应该是：x(17) + t(1) + u(1) + u_x(17) = 36维，但期望35维
        # 查看原始代码，应该是：x(17) + u(1) + u_x(17) + u_t(1) = 36维，但期望35维
        # 实际上，应该去掉一个维度，可能是t维度
        
        F_input = torch.cat([x, u, u_x, u_t], dim=1)  # 17+1+17+1=36维
        # 如果dynamical_F期望35维，我们需要去掉一个维度，这里去掉t维度
        # F_input = torch.cat([x, u, u_x, u_t], dim=1)[:, :-1]  # 去掉最后一个维度
        
        # 但我们先尝试直接使用，如果出错再调整
        F = self.dynamical_F(F_input)
        f = u_t - F
        
        return u, f

class AHNUDataProcessor:
    """AHNU数据处理器 - 适配PINN模型"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.scaler = StandardScaler()
        self.nominal_capacity = 1200.0
        
    def load_and_process_data(self):
        """加载并处理AHNU数据，适配PINN格式"""
        print(f"📊 加载AHNU数据: {self.file_path}")
        
        # 读取Excel文件
        df = pd.read_excel(self.file_path)
        print(f"原始数据形状: {df.shape}")
        
        # 提取17维特征
        features_17d = self._extract_17_features(df)
        
        # 计算SOH标签
        soh_labels = self._calculate_soh(df)
        
        # 添加时间维度（PINN需要时间信息）
        time_steps = np.arange(len(features_17d)) / len(features_17d)  # 归一化时间 [0,1]
        
        # 组合特征和时间：[17维特征 + 1维时间] = 18维
        features_with_time = np.column_stack([features_17d, time_steps.reshape(-1, 1)])
        
        # 创建序列对用于PINN训练
        X1, X2, Y1, Y2 = self._create_sequence_pairs(features_with_time, soh_labels)
        
        return X1, X2, Y1, Y2
    
    def _extract_17_features(self, df):
        """从AHNU数据提取17维统计特征"""
        features = []
        
        # 选择关键的电池参数列进行特征提取
        key_columns = [
            '充电比容量(mAh/g)', '放电比容量(mAh/g)', '充放电效率(%)',
            '中值电压(V)', '充电时间(h)', '放电时间(h)', 
            '充电平均电压(V)', '放电平均电压(V)'
        ]
        
        for _, row in df.iterrows():
            row_features = []
            
            # 1-8: 各列的当前值
            for col in key_columns:
                row_features.append(row[col])
            
            # 9-10: 容量相关比率
            charge_cap = row['充电比容量(mAh/g)']
            discharge_cap = row['放电比容量(mAh/g)']
            row_features.append(charge_cap / self.nominal_capacity)
            row_features.append(discharge_cap / self.nominal_capacity)
            
            # 11-12: 电压差和时间比
            voltage_diff = row['充电平均电压(V)'] - row['放电平均电压(V)']
            time_ratio = row['充电时间(h)'] / (row['放电时间(h)'] + 1e-6)
            row_features.append(voltage_diff)
            row_features.append(time_ratio)
            
            # 13-14: 效率相关特征
            efficiency = row['充放电效率(%)']
            row_features.append(efficiency / 100.0)
            row_features.append(np.log(efficiency + 1))
            
            # 15-16: 循环相关特征
            cycle_num = row['循环号']
            row_features.append(cycle_num / 216.0)
            row_features.append(np.sqrt(cycle_num))
            
            # 17: 综合健康指标
            health_indicator = (discharge_cap * efficiency / 100.0) / self.nominal_capacity
            row_features.append(health_indicator)
            
            features.append(row_features)
        
        features_array = np.array(features)
        print(f"提取的17维特征形状: {features_array.shape}")
        
        # 标准化特征
        features_array = self.scaler.fit_transform(features_array)
        
        return features_array
    
    def _calculate_soh(self, df):
        """计算SOH值"""
        # 使用放电容量计算SOH
        discharge_capacity = df['放电比容量(mAh/g)'].values
        
        # 相对于初始容量的SOH
        initial_capacity = discharge_capacity[0]
        soh_values = discharge_capacity / initial_capacity
        
        # 确保SOH在合理范围内
        soh_values = np.clip(soh_values, 0.1, 1.2)
        
        print(f"SOH值范围: [{soh_values.min():.4f}, {soh_values.max():.4f}]")
        return soh_values.reshape(-1, 1)
    
    def _create_sequence_pairs(self, features, soh_labels):
        """创建序列对用于PINN训练"""
        n_samples = len(features)
        
        # 创建连续的样本对 (t, t+1)
        X1, X2 = [], []
        Y1, Y2 = [], []
        
        for i in range(n_samples - 1):
            X1.append(features[i])
            X2.append(features[i + 1]) 
            Y1.append(soh_labels[i])
            Y2.append(soh_labels[i + 1])
        
        return np.array(X1), np.array(X2), np.array(Y1), np.array(Y2)

def get_args():
    """获取PINN模型参数"""
    parser = argparse.ArgumentParser()
    
    # 数据相关
    parser.add_argument('--data', type=str, default='AHNU', help='数据集名称')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--normalization_method', type=str, default='z-score', help='标准化方法')
    
    # 训练相关
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')  # 减少训练轮数以加快测试
    parser.add_argument('--lr', type=float, default=1e-3, help='学习率')
    parser.add_argument('--warmup_epochs', type=int, default=10, help='预热轮数')
    parser.add_argument('--warmup_lr', type=float, default=5e-4, help='预热学习率')
    parser.add_argument('--final_lr', type=float, default=1e-4, help='最终学习率')
    parser.add_argument('--lr_F', type=float, default=1e-3, help='动力学网络学习率')
    parser.add_argument('--iter_per_epoch', type=int, default=1, help='每轮迭代数')
    
    # 模型架构
    parser.add_argument('--F_layers_num', type=int, default=3, help='动力学网络层数')
    parser.add_argument('--F_hidden_dim', type=int, default=32, help='动力学网络隐藏维度')  # 减小以防过拟合
    
    # 损失权重 - 根据经验调整
    parser.add_argument('--alpha', type=float, default=0.1, help='PDE损失权重')  # 减小PDE权重
    parser.add_argument('--beta', type=float, default=0.5, help='物理损失权重')  # 减小物理约束权重
    
    # 早停和保存
    parser.add_argument('--early_stop', type=int, default=30, help='早停耐心值')
    parser.add_argument('--save_folder', type=str, default='./AHNU_Project/results', help='保存目录')
    parser.add_argument('--log_dir', type=str, default='pinn_full_test.log', help='日志文件')
    
    return parser.parse_args()

def main():
    """主函数"""
    print("🚀 AHNU数据集完整PINN模型测试")
    print(f"设备: {device}")
    
    # 获取参数
    args = get_args()
    
    # 创建保存目录
    os.makedirs(args.save_folder, exist_ok=True)
    
    # 数据文件路径
    data_file = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
    
    # 加载数据
    processor = AHNUDataProcessor(data_file)
    X1, X2, Y1, Y2 = processor.load_and_process_data()
    
    # 转换为张量
    X1 = torch.from_numpy(X1).float()
    X2 = torch.from_numpy(X2).float()
    Y1 = torch.from_numpy(Y1).float()
    Y2 = torch.from_numpy(Y2).float()
    
    print(f"特征形状: X1={X1.shape}, Y1={Y1.shape}")
    print(f"输入维度: {X1.shape[1]} (17维特征 + 1维时间)")
    
    # 划分数据集
    indices = torch.arange(len(X1))
    train_idx, temp_idx = train_test_split(indices, test_size=0.4, random_state=42)
    valid_idx, test_idx = train_test_split(temp_idx, test_size=0.5, random_state=42)
    
    print(f"训练集: {len(train_idx)}, 验证集: {len(valid_idx)}, 测试集: {len(test_idx)}")
    
    # 创建数据加载器
    train_dataset = TensorDataset(X1[train_idx], X2[train_idx], Y1[train_idx], Y2[train_idx])
    valid_dataset = TensorDataset(X1[valid_idx], X2[valid_idx], Y1[valid_idx], Y2[valid_idx])
    test_dataset = TensorDataset(X1[test_idx], X2[test_idx], Y1[test_idx], Y2[test_idx])
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    valid_loader = DataLoader(valid_dataset, batch_size=args.batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)
    
    print(f"\n{'='*60}")
    print("🔋 创建完整PINN模型")
    print(f"{'='*60}")
    
    # 创建PINN模型
    model = PINN(args).to(device)
    
    # 打印模型信息
    solution_u_params = sum(p.numel() for p in model.solution_u.parameters() if p.requires_grad)
    dynamical_F_params = sum(p.numel() for p in model.dynamical_F.parameters() if p.requires_grad)
    total_params = solution_u_params + dynamical_F_params
    
    print(f"📊 模型参数统计:")
    print(f"  - Solution_u网络: {solution_u_params:,} 参数")
    print(f"  - Dynamical_F网络: {dynamical_F_params:,} 参数")
    print(f"  - 总参数量: {total_params:,} 参数")
    print(f"  - 参数/样本比: {total_params/len(train_idx):.2f}")
    
    if total_params / len(train_idx) > 10:
        print(f"⚠️  警告: 参数/样本比 > 10，可能过拟合！")
    
    print(f"\n🏃 开始训练...")
    print(f"  - 损失权重: 数据=1.0, PDE={args.alpha}, 物理={args.beta}")
    
    try:
        # 训练模型
        model.Train(train_loader, test_loader, valid_loader)
        
        print(f"\n🧪 模型评估...")
        
        # 加载最佳模型进行测试
        if hasattr(model, 'best_model') and model.best_model is not None:
            model.solution_u.load_state_dict(model.best_model['solution_u'])
            model.dynamical_F.load_state_dict(model.best_model['dynamical_F'])
        
        # 测试模型
        true_labels, pred_labels = model.Test(test_loader)
        
        # 计算指标
        [MAE, MAPE, MSE, RMSE] = eval_metrix(pred_labels, true_labels)
        r2 = r2_score(true_labels.flatten(), pred_labels.flatten())
        
        # SOH违规率检查
        violations = np.sum((pred_labels < 0.1) | (pred_labels > 1.2))
        violation_rate = violations / len(pred_labels)
        
        print(f"\n📈 完整PINN模型结果:")
        print(f"  - MSE: {MSE:.8f}")
        print(f"  - MAE: {MAE:.8f}")
        print(f"  - MAPE: {MAPE:.6f}%")
        print(f"  - RMSE: {RMSE:.8f}")
        print(f"  - R²: {r2:.6f}")
        print(f"  - SOH违规率: {violation_rate:.4f}")
        
        # 保存结果
        results_file = os.path.join(args.save_folder, 'pinn_full_results.npz')
        np.savez(results_file, 
                 true_labels=true_labels, 
                 pred_labels=pred_labels,
                 mse=MSE, mae=MAE, r2=r2, violation_rate=violation_rate)
        
        print(f"\n📁 结果已保存到: {results_file}")
        
        # 对比基线模型结果
        print(f"\n📊 与基线模型对比:")
        print(f"  基线MLP:    MSE=0.00029146, MAE=0.01167682, R²=0.965473")
        print(f"  完整PINN:   MSE={MSE:.8f}, MAE={MAE:.8f}, R²={r2:.6f}")
        
        if r2 > 0.9:
            print(f"✅ PINN模型表现良好！")
        elif r2 > 0:
            print(f"⚠️  PINN模型表现一般，但优于随机预测")
        else:
            print(f"❌ PINN模型表现较差，可能存在过拟合")
            print(f"💡 建议: 减少模型复杂度或增加正则化")
            
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()