#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版PINN测试
测试物理信息神经网络在AHNU数据上的基本性能
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

device = 'cuda' if torch.cuda.is_available() else 'cpu'

class SimpleMLP(nn.Module):
    """简单的MLP网络"""
    def __init__(self, input_dim=17, hidden_dim=64, output_dim=1):
        super(SimpleMLP, self).__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
    
    def forward(self, x):
        return self.net(x)

class SimplePINN(nn.Module):
    """简化版物理信息神经网络"""
    def __init__(self, input_dim=17):
        super(SimplePINN, self).__init__()
        # 解网络：预测SOH
        self.solution_net = SimpleMLP(input_dim, 64, 1)
        
        # 物理约束网络：学习退化规律
        self.physics_net = SimpleMLP(input_dim + 2, 32, 1)  # 输入：特征+SOH+时间
        
    def forward(self, x, t):
        """
        x: 17维特征
        t: 1维时间
        """
        # 确保输入需要梯度
        x = x.requires_grad_(True)
        t = t.requires_grad_(True)
        
        # 预测SOH
        u = self.solution_net(x)
        
        # 计算SOH对时间的导数（数值近似）
        u_t = torch.autograd.grad(u.sum(), t, create_graph=True, allow_unused=True)[0]
        if u_t is None:
            u_t = torch.zeros_like(t)
        
        # 物理约束：退化应该是单调递减的
        # 简化版物理方程：u_t + f(x,t,u) = 0
        physics_input = torch.cat([x, u, t], dim=1)
        f = self.physics_net(physics_input)
        
        # PDE残差
        pde_residual = u_t + f
        
        return u, pde_residual

class AHNUDataProcessor:
    """AHNU数据处理器"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.scaler = StandardScaler()
        self.nominal_capacity = 1200.0
        
    def load_and_process_data(self):
        """加载并处理AHNU数据"""
        print(f"📊 加载AHNU数据: {self.file_path}")
        
        # 读取Excel文件
        df = pd.read_excel(self.file_path)
        print(f"原始数据形状: {df.shape}")
        
        # 提取17维特征
        features_17d = self._extract_17_features(df)
        
        # 计算SOH标签
        soh_labels = self._calculate_soh(df)
        
        # 创建时间序列
        time_steps = np.arange(len(features_17d)).reshape(-1, 1) / len(features_17d)
        
        return features_17d, soh_labels, time_steps
    
    def _extract_17_features(self, df):
        """从AHNU数据提取17维统计特征"""
        features = []
        
        # 选择关键的电池参数列进行特征提取
        key_columns = [
            '充电比容量(mAh/g)', '放电比容量(mAh/g)', '充放电效率(%)',
            '中值电压(V)', '充电时间(h)', '放电时间(h)', 
            '充电平均电压(V)', '放电平均电压(V)'
        ]
        
        for _, row in df.iterrows():
            row_features = []
            
            # 1-8: 各列的当前值
            for col in key_columns:
                row_features.append(row[col])
            
            # 9-10: 容量相关比率
            charge_cap = row['充电比容量(mAh/g)']
            discharge_cap = row['放电比容量(mAh/g)']
            row_features.append(charge_cap / self.nominal_capacity)
            row_features.append(discharge_cap / self.nominal_capacity)
            
            # 11-12: 电压差和时间比
            voltage_diff = row['充电平均电压(V)'] - row['放电平均电压(V)']
            time_ratio = row['充电时间(h)'] / (row['放电时间(h)'] + 1e-6)
            row_features.append(voltage_diff)
            row_features.append(time_ratio)
            
            # 13-14: 效率相关特征
            efficiency = row['充放电效率(%)']
            row_features.append(efficiency / 100.0)
            row_features.append(np.log(efficiency + 1))
            
            # 15-16: 循环相关特征
            cycle_num = row['循环号']
            row_features.append(cycle_num / 216.0)
            row_features.append(np.sqrt(cycle_num))
            
            # 17: 综合健康指标
            health_indicator = (discharge_cap * efficiency / 100.0) / self.nominal_capacity
            row_features.append(health_indicator)
            
            features.append(row_features)
        
        features_array = np.array(features)
        print(f"提取的17维特征形状: {features_array.shape}")
        
        # 标准化特征
        features_array = self.scaler.fit_transform(features_array)
        
        return features_array
    
    def _calculate_soh(self, df):
        """计算SOH值"""
        # 使用放电容量计算SOH
        discharge_capacity = df['放电比容量(mAh/g)'].values
        
        # 相对于初始容量的SOH
        initial_capacity = discharge_capacity[0]
        soh_values = discharge_capacity / initial_capacity
        
        # 确保SOH在合理范围内
        soh_values = np.clip(soh_values, 0.1, 1.2)
        
        print(f"SOH值范围: [{soh_values.min():.4f}, {soh_values.max():.4f}]")
        return soh_values.reshape(-1, 1)

def train_model(model, train_loader, valid_loader, epochs=100, lr=1e-3):
    """训练模型"""
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10)
    mse_loss = nn.MSELoss()
    
    best_valid_loss = float('inf')
    patience_counter = 0
    
    print(f"开始训练，总轮数: {epochs}")
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        pde_loss = 0.0
        data_loss = 0.0
        
        for batch_idx, (x, t, y) in enumerate(train_loader):
            x, t, y = x.to(device).requires_grad_(True), t.to(device).requires_grad_(True), y.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            u_pred, pde_res = model(x, t)
            
            # 数据损失
            loss_data = mse_loss(u_pred, y)
            
            # PDE损失（残差应该接近0）
            loss_pde = mse_loss(pde_res, torch.zeros_like(pde_res))
            
            # 总损失
            total_loss = loss_data + 0.1 * loss_pde
            
            total_loss.backward()
            optimizer.step()
            
            train_loss += total_loss.item()
            data_loss += loss_data.item()
            pde_loss += loss_pde.item()
        
        avg_train_loss = train_loss / len(train_loader)
        avg_data_loss = data_loss / len(train_loader)
        avg_pde_loss = pde_loss / len(train_loader)
        
        # 验证阶段
        model.eval()
        valid_loss = 0.0
        with torch.no_grad():
            for x, t, y in valid_loader:
                x, t, y = x.to(device).requires_grad_(True), t.to(device).requires_grad_(True), y.to(device)
                u_pred, _ = model(x, t)
                valid_loss += mse_loss(u_pred, y).item()
        
        avg_valid_loss = valid_loss / len(valid_loader)
        scheduler.step(avg_valid_loss)
        
        # 早停检查
        if avg_valid_loss < best_valid_loss:
            best_valid_loss = avg_valid_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'best_simple_pinn.pth')
        else:
            patience_counter += 1
        
        # 进度报告
        if (epoch + 1) % 20 == 0 or epoch == 0:
            print(f"轮次 {epoch+1:3d}/{epochs}: "
                  f"训练={avg_train_loss:.6f}, "
                  f"数据={avg_data_loss:.6f}, "
                  f"PDE={avg_pde_loss:.6f}, "
                  f"验证={avg_valid_loss:.6f}")
        
        # 早停
        if patience_counter >= 30:
            print(f"在第 {epoch+1} 轮早停")
            break
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_simple_pinn.pth'))
    
    return model

def evaluate_model(model, test_loader):
    """评估模型"""
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for x, t, y in test_loader:
            x, t, y = x.to(device).requires_grad_(True), t.to(device).requires_grad_(True), y.to(device)
            u_pred, _ = model(x, t)
            all_predictions.append(u_pred.cpu().numpy())
            all_targets.append(y.cpu().numpy())
    
    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)
    
    # 计算指标
    mse = mean_squared_error(targets, predictions)
    mae = mean_absolute_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    
    # SOH违规率检查
    violations = np.sum((predictions < 0.1) | (predictions > 1.2))
    violation_rate = violations / len(predictions)
    
    return {
        'mse': mse,
        'mae': mae,
        'r2': r2,
        'violation_rate': violation_rate,
        'predictions': predictions,
        'targets': targets
    }

def main():
    """主函数"""
    print("🚀 AHNU数据集简化版PINN测试")
    print(f"设备: {device}")
    
    # 数据文件路径
    data_file = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
    
    # 加载数据
    processor = AHNUDataProcessor(data_file)
    features, labels, time_steps = processor.load_and_process_data()
    
    # 转换为张量
    X = torch.from_numpy(features).float()
    y = torch.from_numpy(labels).float()
    t = torch.from_numpy(time_steps).float()
    
    print(f"特征形状: {X.shape}, 时间形状: {t.shape}, 标签形状: {y.shape}")
    
    # 划分数据集
    X_train, X_temp, t_train, t_temp, y_train, y_temp = train_test_split(
        X, t, y, test_size=0.4, random_state=42
    )
    X_valid, X_test, t_valid, t_test, y_valid, y_test = train_test_split(
        X_temp, t_temp, y_temp, test_size=0.5, random_state=42
    )
    
    print(f"训练集: {len(X_train)}, 验证集: {len(X_valid)}, 测试集: {len(X_test)}")
    
    # 创建数据加载器
    batch_size = 32
    train_loader = DataLoader(TensorDataset(X_train, t_train, y_train), batch_size=batch_size, shuffle=True)
    valid_loader = DataLoader(TensorDataset(X_valid, t_valid, y_valid), batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(TensorDataset(X_test, t_test, y_test), batch_size=batch_size, shuffle=False)
    
    # 创建模型
    model = SimplePINN(input_dim=17).to(device)
    
    # 计算参数数量
    param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数数量: {param_count:,}")
    
    # 训练模型
    print(f"\n{'='*60}")
    print("🔋 开始训练简化版PINN模型")
    print(f"{'='*60}")
    
    model = train_model(model, train_loader, valid_loader, epochs=200, lr=1e-3)
    
    # 评估模型
    print(f"\n🧪 评估简化版PINN模型...")
    result = evaluate_model(model, test_loader)
    
    print(f"\n📈 简化版PINN结果:")
    print(f"  - MSE: {result['mse']:.8f}")
    print(f"  - MAE: {result['mae']:.8f}")
    print(f"  - R²:  {result['r2']:.6f}")
    print(f"  - SOH违规率: {result['violation_rate']:.4f}")
    
    # 对比基线结果
    print(f"\n📊 与基线模型对比:")
    print(f"  基线MLP:    MSE=0.00029146, MAE=0.01167682, R²=0.965473")
    print(f"  简化PINN:   MSE={result['mse']:.8f}, MAE={result['mae']:.8f}, R²={result['r2']:.6f}")
    
    if result['r2'] > 0.9:
        print(f"✅ 简化PINN模型表现良好！")
    elif result['r2'] > 0:
        print(f"⚠️  简化PINN模型表现一般，但优于随机预测")
    else:
        print(f"❌ 简化PINN模型表现较差")
        print(f"💡 建议: 调整损失权重或模型架构")

if __name__ == "__main__":
    main()