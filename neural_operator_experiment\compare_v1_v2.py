#!/usr/bin/env python3
"""
Compare performance between DeepONet-BNN-PINN v1 and v2
"""

import numpy as np
import matplotlib.pyplot as plt
import scienceplots
import os
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

def load_results(results_dir, version):
    """Load prediction results from saved files"""
    results = {}
    
    try:
        if version == 'v1':
            results['true'] = np.load(os.path.join(results_dir, 'true_labels_all_deeponet_bnn.npy'))
            results['pred_mean'] = np.load(os.path.join(results_dir, 'pred_mean_all_deeponet_bnn.npy'))
            results['pred_std'] = np.load(os.path.join(results_dir, 'pred_std_all_deeponet_bnn.npy'))
        elif version == 'v2':
            results['true'] = np.load(os.path.join(results_dir, 'true_labels_all_v2.npy'))
            results['pred_mean'] = np.load(os.path.join(results_dir, 'pred_mean_all_v2.npy'))
            results['pred_std'] = np.load(os.path.join(results_dir, 'pred_std_all_v2.npy'))
        
        print(f"✓ {version.upper()} results loaded successfully")
        return results
    except FileNotFoundError as e:
        print(f"✗ {version.upper()} results not found: {e}")
        return None

def calculate_metrics(true, pred):
    """Calculate comprehensive performance metrics"""
    true_flat = true.flatten()
    pred_flat = pred.flatten()
    
    mse = mean_squared_error(true_flat, pred_flat)
    mae = mean_absolute_error(true_flat, pred_flat)
    r2 = r2_score(true_flat, pred_flat)
    rmse = np.sqrt(mse)
    
    # Additional metrics
    mape = np.mean(np.abs((true_flat - pred_flat) / (true_flat + 1e-8))) * 100
    max_error = np.max(np.abs(true_flat - pred_flat))
    
    # SOH constraint violations
    violations = np.sum((pred_flat < 0) | (pred_flat > 1))
    violation_rate = violations / len(pred_flat)
    
    return {
        'MSE': mse,
        'MAE': mae,
        'RMSE': rmse,
        'R2': r2,
        'MAPE': mape,
        'Max_Error': max_error,
        'SOH_Violations': violations,
        'Violation_Rate': violation_rate
    }

def plot_comparison(v1_results, v2_results, save_path):
    """Create comprehensive comparison plot"""
    plt.style.use(['science', 'nature'])
    fig, axes = plt.subplots(2, 3, figsize=(18, 10), dpi=200)
    
    # Plot 1: V1 Predictions
    ax = axes[0, 0]
    if v1_results:
        ax.plot(v1_results['true'], label='Ground Truth', color='#403990', linewidth=2, zorder=3)
        ax.plot(v1_results['pred_mean'], label='DeepONet-BNN-PINN v1', color='#CF3D3E', linewidth=1.5, zorder=2)
        ax.fill_between(range(len(v1_results['pred_mean'])), 
                        (v1_results['pred_mean'] - 2*v1_results['pred_std']).flatten(), 
                        (v1_results['pred_mean'] + 2*v1_results['pred_std']).flatten(), 
                        color='#FBDD85', alpha=0.3, label='95% CI', zorder=1)
    ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax.axhline(y=1, color='red', linestyle='--', alpha=0.5)
    ax.set_title('DeepONet-BNN-PINN v1')
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('SOH')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Plot 2: V2 Predictions
    ax = axes[0, 1]
    if v2_results:
        ax.plot(v2_results['true'], label='Ground Truth', color='#403990', linewidth=2, zorder=3)
        ax.plot(v2_results['pred_mean'], label='DeepONet-BNN-PINN v2', color='#2E8B57', linewidth=1.5, zorder=2)
        ax.fill_between(range(len(v2_results['pred_mean'])), 
                        (v2_results['pred_mean'] - 2*v2_results['pred_std']).flatten(), 
                        (v2_results['pred_mean'] + 2*v2_results['pred_std']).flatten(), 
                        color='#98FB98', alpha=0.3, label='95% CI', zorder=1)
    ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax.axhline(y=1, color='red', linestyle='--', alpha=0.5)
    ax.set_title('DeepONet-BNN-PINN v2 (Enhanced)')
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('SOH')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Plot 3: Direct Comparison
    ax = axes[0, 2]
    if v1_results and v2_results:
        ax.plot(v1_results['true'], label='Ground Truth', color='#403990', linewidth=2, zorder=4)
        ax.plot(v1_results['pred_mean'], label='v1 Prediction', color='#CF3D3E', linewidth=1.5, linestyle='--', zorder=3)
        ax.plot(v2_results['pred_mean'], label='v2 Prediction', color='#2E8B57', linewidth=1.5, linestyle=':', zorder=2)
    ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax.axhline(y=1, color='red', linestyle='--', alpha=0.5)
    ax.set_title('Version Comparison')
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('SOH')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Plot 4: Error Comparison
    ax = axes[1, 0]
    if v1_results and v2_results:
        v1_errors = np.abs(v1_results['pred_mean'].flatten() - v1_results['true'].flatten())
        v2_errors = np.abs(v2_results['pred_mean'].flatten() - v2_results['true'].flatten())
        ax.plot(v1_errors, label='v1 Absolute Error', color='#CF3D3E', linewidth=1, alpha=0.7)
        ax.plot(v2_errors, label='v2 Absolute Error', color='#2E8B57', linewidth=1, alpha=0.7)
    ax.set_title('Absolute Error Comparison')
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('Absolute Error')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Plot 5: Uncertainty Comparison
    ax = axes[1, 1]
    if v1_results and v2_results:
        ax.plot(v1_results['pred_std'].flatten(), label='v1 Uncertainty', color='#CF3D3E', linewidth=1, alpha=0.7)
        ax.plot(v2_results['pred_std'].flatten(), label='v2 Uncertainty', color='#2E8B57', linewidth=1, alpha=0.7)
    ax.set_title('Uncertainty Comparison')
    ax.set_xlabel('Sample Index')
    ax.set_ylabel('Prediction Std')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Plot 6: Scatter Plot Comparison
    ax = axes[1, 2]
    if v1_results and v2_results:
        ax.scatter(v1_results['true'].flatten(), v1_results['pred_mean'].flatten(), 
                  alpha=0.5, s=1, color='#CF3D3E', label='v1')
        ax.scatter(v2_results['true'].flatten(), v2_results['pred_mean'].flatten(), 
                  alpha=0.5, s=1, color='#2E8B57', label='v2')
        min_val = min(v1_results['true'].min(), v2_results['true'].min())
        max_val = max(v1_results['true'].max(), v2_results['true'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2, label='Perfect')
    ax.set_title('True vs Predicted')
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Comparison plot saved to: {save_path}")

def create_metrics_table(v1_metrics, v2_metrics):
    """Create a formatted metrics comparison table"""
    print("\n" + "="*80)
    print("📊 PERFORMANCE COMPARISON: DeepONet-BNN-PINN v1 vs v2")
    print("="*80)
    
    if v1_metrics and v2_metrics:
        print(f"{'Metric':<15} {'v1':<15} {'v2':<15} {'Improvement':<15} {'Status'}")
        print("-" * 80)
        
        for metric in ['MSE', 'MAE', 'RMSE', 'R2', 'MAPE', 'Max_Error', 'Violation_Rate']:
            v1_val = v1_metrics[metric]
            v2_val = v2_metrics[metric]
            
            if metric == 'R2':  # Higher is better for R2
                improvement = ((v2_val - v1_val) / abs(v1_val)) * 100 if v1_val != 0 else 0
                status = "✅ Better" if v2_val > v1_val else "❌ Worse"
            else:  # Lower is better for other metrics
                improvement = ((v1_val - v2_val) / abs(v1_val)) * 100 if v1_val != 0 else 0
                status = "✅ Better" if v2_val < v1_val else "❌ Worse"
            
            print(f"{metric:<15} {v1_val:<15.6f} {v2_val:<15.6f} {improvement:<+14.2f}% {status}")
        
        print("-" * 80)
        print(f"SOH Violations: v1={v1_metrics['SOH_Violations']}, v2={v2_metrics['SOH_Violations']}")
        
    elif v1_metrics:
        print("Only v1 results available:")
        for metric, value in v1_metrics.items():
            print(f"  {metric}: {value:.6f}")
    elif v2_metrics:
        print("Only v2 results available:")
        for metric, value in v2_metrics.items():
            print(f"  {metric}: {value:.6f}")

def main():
    # Directories
    results_dir_v1 = './neural_operator_experiment/results'
    results_dir_v2 = './neural_operator_experiment/results_v2'
    plots_dir = './neural_operator_experiment/plots_comparison'
    os.makedirs(plots_dir, exist_ok=True)
    
    print("🔍 Loading results...")
    
    # Load results
    v1_results = load_results(results_dir_v1, 'v1')
    v2_results = load_results(results_dir_v2, 'v2')
    
    # Calculate metrics
    v1_metrics = None
    v2_metrics = None
    
    if v1_results:
        v1_metrics = calculate_metrics(v1_results['true'], v1_results['pred_mean'])
        print("✓ v1 metrics calculated")
    
    if v2_results:
        v2_metrics = calculate_metrics(v2_results['true'], v2_results['pred_mean'])
        print("✓ v2 metrics calculated")
    
    # Create comparison table
    create_metrics_table(v1_metrics, v2_metrics)
    
    # Create comparison plot
    if v1_results or v2_results:
        comparison_plot_path = os.path.join(plots_dir, 'v1_vs_v2_comparison.png')
        plot_comparison(v1_results, v2_results, comparison_plot_path)
    
    # Summary
    print("\n" + "="*80)
    print("🎯 SUMMARY")
    print("="*80)
    
    if v1_metrics and v2_metrics:
        key_improvements = []
        if v2_metrics['Violation_Rate'] < v1_metrics['Violation_Rate']:
            key_improvements.append(f"SOH constraint violations: {v1_metrics['SOH_Violations']} → {v2_metrics['SOH_Violations']}")
        if v2_metrics['MAE'] < v1_metrics['MAE']:
            key_improvements.append(f"Mean Absolute Error improved by {((v1_metrics['MAE'] - v2_metrics['MAE'])/v1_metrics['MAE']*100):.2f}%")
        if v2_metrics['MSE'] < v1_metrics['MSE']:
            key_improvements.append(f"Mean Squared Error improved by {((v1_metrics['MSE'] - v2_metrics['MSE'])/v1_metrics['MSE']*100):.2f}%")
        
        if key_improvements:
            print("✅ Key Improvements in v2:")
            for improvement in key_improvements:
                print(f"  • {improvement}")
        else:
            print("⚠️  v2 shows mixed results compared to v1")
    
    print(f"\n📁 Comparison plots saved in: {plots_dir}")
    print("🎉 Comparison analysis completed!")

if __name__ == "__main__":
    main()
