# 两阶段训练策略实现说明

## 🎯 概述

本文档说明了在 `main_deeponet_bnn_pinn_v3.py` 中实现的两阶段训练策略，该策略模仿了 Model 文件夹下的训练逻辑：先进行联合训练，然后冻结 `dynamical_F` 的权重，只对 `solution_u` 进行微调。

## 📋 训练策略对比

### 原始单阶段训练
- **训练方式**: 同时训练 `solution_u` 和 `dynamical_F`
- **优化器**: 两个优化器同步更新
- **问题**: 可能存在过拟合，两个网络相互干扰

### 新的两阶段训练
- **阶段一**: 联合训练两个网络，建立基础映射关系
- **阶段二**: 冻结 `dynamical_F`，只微调 `solution_u`，精细化解的表示

## 🔧 关键修改

### 1. 训练函数扩展
```python
def train_one_epoch(self, dataloader, stage="joint"):
    """
    训练一个epoch
    Args:
        dataloader: 数据加载器
        stage: "joint" 表示联合训练, "finetune" 表示仅训练solution_u
    """
```

### 2. 冻结/解冻功能
```python
def freeze_dynamical_f(self):
    """冻结dynamical_F的参数"""
    for param in self.dynamical_F.parameters():
        param.requires_grad = False

def unfreeze_dynamical_f(self):
    """解冻dynamical_F的参数"""
    for param in self.dynamical_F.parameters():
        param.requires_grad = True
```

### 3. 阶段性模型保存
```python
def save_stage_model(self, stage_name, save_dir):
    """保存阶段性模型"""
    stage_model = {
        'solution_u': self.solution_u.state_dict(),
        'dynamical_F': self.dynamical_F.state_dict(),
        'optimizer_u': self.optimizer_u.state_dict(),
        'optimizer_f': self.optimizer_f.state_dict(),
        'training_history': self.training_history
    }
```

## 📊 新增参数

在 `get_ahnu_args()` 中添加了以下参数：

```python
# 两阶段训练参数
parser.add_argument('--stage1_epochs', type=int, default=100, help='第一阶段联合训练轮数')
parser.add_argument('--stage2_epochs', type=int, default=50, help='第二阶段微调轮数')
parser.add_argument('--enable_two_stage', action='store_true', default=True, help='启用两阶段训练')
parser.add_argument('--finetune_lr_u', type=float, default=5e-4, help='微调阶段solution_u学习率')
```

## 🚀 使用方法

### 启用两阶段训练 (默认)
```bash
python main_deeponet_bnn_pinn_v3.py --enable_two_stage --stage1_epochs 100 --stage2_epochs 50
```

### 使用传统单阶段训练
```bash
python main_deeponet_bnn_pinn_v3.py --no-enable_two_stage --epochs 150
```

### 自定义微调学习率
```bash
python main_deeponet_bnn_pinn_v3.py --finetune_lr_u 1e-4
```

## 📈 训练流程

### 阶段一: 联合训练
1. 同时训练 `solution_u` 和 `dynamical_F`
2. 使用原始学习率
3. 保存最佳模型作为阶段二起点
4. 早停耐心值: 25 轮

### 阶段二: 微调
1. 冻结 `dynamical_F` 参数
2. 降低 `solution_u` 学习率至微调率
3. 只更新 `solution_u` 的参数
4. 早停耐心值: 15 轮

## 🎯 预期效果

### 理论优势
1. **更稳定的训练**: 避免两个网络相互干扰
2. **精细化解表示**: 在稳定的动力学基础上优化解
3. **更好的泛化**: 减少过拟合风险
4. **更快收敛**: 微调阶段通常收敛更快

### 适用场景
- 小数据集训练
- 复杂的PINN模型
- 需要高精度的SOH预测
- 两个网络耦合较强的情况

## 🧪 验证方法

我们提供了 `test_two_stage_training.py` 脚本来验证策略效果：

```bash
python test_two_stage_training.py
```

该脚本将：
1. 使用XJTU数据进行对比实验
2. 分别测试单阶段和两阶段训练
3. 输出性能对比结果
4. 生成训练历史图表

## 📂 文件结构

```
AHNU_Project/
├── code/
│   ├── main_deeponet_bnn_pinn_v3.py    # ✅ 实现两阶段训练
│   └── ...
├── test_two_stage_training.py          # 🧪 验证脚本
├── results/
│   ├── ahnu_deeponet_bnn_model_stage1_best.pth  # 阶段一最佳模型
│   ├── ahnu_deeponet_bnn_model.pth               # 最终模型
│   └── ...
└── plots/
    ├── two_stage_training_history.png   # 训练历史
    └── ...
```

## 🔍 监控训练

训练过程中会输出详细日志：

```
🎯 开始两阶段训练...

✨ 两阶段训练模式:
  阶段一: 联合训练 solution_u + dynamical_F (100 轮)
  阶段二: 冻结 dynamical_F, 微调 solution_u (50 轮)

💪 阶段一: 联合训练 solution_u + dynamical_F
==========================================================
  阶段一  10/100: 损失=0.045732, 数据=0.012456, PDE=0.023789, 物理=0.009487
  ...
✅ 阶段一完成!最佳模型: results/ahnu_deeponet_bnn_model_stage1_best.pth

🧊 阶段二: 冻结 dynamical_F, 微调 solution_u
==========================================================
🧊 已冻结 dynamical_F 网络参数
📋 微调阶段 solution_u 学习率设为: 0.0005
  阶段二   5/50: 损失=0.032145, 数据=0.008234, PDE=0.018765, 物理=0.005146
  ...
✅ 阶段二完成! 最终模型已保存: results/ahnu_deeponet_bnn_model.pth
```

## 🎉 总结

这个两阶段训练策略模仿了 `Model/Model.py` 和 `main_adaptation - fine-tuning.py` 中的思想：

1. **先预训练**: 建立稳定的特征表示
2. **再微调**: 精细化调整解网络
3. **分阶段优化**: 避免同时优化带来的不稳定性

通过这种方式，我们期望获得更好的SOH预测性能，特别是在AHNU这样的小数据集上。

## 🤔 注意事项

1. **学习率设置**: 微调阶段学习率应该比初始训练小
2. **早停策略**: 微调阶段可以使用更小的耐心值
3. **模型加载**: 确保正确加载阶段一的最佳模型
4. **内存管理**: 冻结参数不会释放内存，只是停止梯度计算

这样的设计让我们能够充分利用PINN的物理约束优势，同时避免复杂模型在小数据集上的过拟合问题。