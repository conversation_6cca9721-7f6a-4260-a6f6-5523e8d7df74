# XJTU电池数据17维特征提取

## 项目概述

本项目实现了从XJTU电池循环数据中提取17维特征的完整流程，用于PINN4SOH（物理信息神经网络）模型的电池健康状态预测。

## 数据说明

### 输入数据格式
- **文件**: `data/cycledata.csv`
- **数据量**: 55,592行 × 9列
- **循环数**: 335个充放电循环
- **列说明**:
  - `循环号`: 充放电循环编号 (1-335)
  - `工步号`: 同一循环内的工步编号 (1=放电, 2=充电)
  - `工步类型`: 恒流放电/恒流充电
  - `总时间(h)`: 累积时间
  - `电流(mA)`: 瞬时电流
  - `电压(V)`: 瞬时电压
  - `比容量(mAh/g)`: 累积比容量
  - `dQ/dV(mAh/V)`: 微分容量
  - `dQm/dV(mAh/V.g)`: 质量归一化微分容量

### 输出特征格式
- **维度**: 17维 (16维特征 + 1维SOH标签)
- **样本数**: 335个循环
- **特征说明**:

#### 前8维：电压相关特征
1. `voltage_mean`: 电压均值
2. `voltage_std`: 电压标准差  
3. `voltage_kurtosis`: 电压峰度
4. `voltage_skewness`: 电压偏度
5. `CC_Q`: 恒流充电电量 (∫I·dt)
6. `CC_time`: 恒流充电时间
7. `voltage_slope`: 电压斜率
8. `voltage_entropy`: 电压熵

#### 中8维：电流相关特征
9. `current_mean`: 电流均值
10. `current_std`: 电流标准差
11. `current_kurtosis`: 电流峰度
12. `current_skewness`: 电流偏度
13. `CV_Q`: 恒压充电电量 (∫I·dt)
14. `CV_time`: 恒压充电时间
15. `current_slope`: 电流斜率
16. `current_entropy`: 电流熵

#### 第17维：SOH标签
17. `SOH`: 健康状态 = 当前容量/初始容量

## 核心算法

### SOH计算
```python
SOH = 当前循环容量 / 第一循环容量
```
其中容量通过放电过程的最大比容量值获得。

### 统计特征计算
```python
# 均值
mean = Σx(i) / n

# 标准差  
std = √(Σ(x(i) - mean)² / (n-1))

# 峰度
kurtosis = Σ(x(i) - mean)⁴ / ((n-1)σ⁴)

# 偏度
skewness = Σ(x(i) - mean)³ / ((n-1)σ³)

# 斜率
slope = (x_end - x_start) / (t_end - t_start)

# 熵
entropy = -Σp(i) * log(p(i))
```

### 电量积分
```python
# 数值积分计算电量
Q = Σ(I_avg * Δt)
```

## 使用方法

### 1. 特征提取
```bash
# 完整特征提取
python extract_xjtu_features.py

# 演示和分析
python feature_extraction_demo.py
```

### 2. 输出文件
- `AHNU_17_features.csv`: 完整特征表格
- `AHNU_features_X.npy`: 16维特征矩阵 (335×16)
- `AHNU_features_y.npy`: SOH标签向量 (335×1)
- `AHNU_features_analysis.png`: 数据分析图表

### 3. 数据加载示例
```python
import numpy as np
import pandas as pd

# 方法1：加载CSV文件
df = pd.read_csv('data/AHNU_17_features.csv')
X = df.iloc[:, 1:-1].values  # 16维特征
y = df['SOH'].values         # SOH标签

# 方法2：加载numpy文件
X = np.load('data/AHNU_features_X.npy')  # (335, 16)
y = np.load('data/AHNU_features_y.npy')  # (335,)
```

## 数据统计

### 基本信息
- **样本数量**: 335个循环
- **特征维度**: 16维
- **SOH范围**: 0.1721 - 1.1141
- **退化率**: 81.05% (从1.0降至0.1895)
- **循环寿命**: 335个循环

### 特征范围
| 特征 | 最小值 | 最大值 | 单位 |
|------|--------|--------|------|
| voltage_mean | 0.5871 | 1.1468 | V |
| voltage_std | 0.5346 | 0.9639 | V |
| CC_Q | 0.0000 | 0.0005 | Ah |
| CC_time | 1.4310 | 10.3270 | h |
| current_mean | -0.0001 | 0.0000 | A |
| current_std | 0.0001 | 0.0001 | A |

## 应用场景

### 1. PINN4SOH模型训练
```python
# 用于物理信息神经网络训练
model = PINN4SOH(input_dim=16, output_dim=1)
model.train(X, y)
```

### 2. 传统机器学习
```python
# 用于MLP、CNN等传统方法
from sklearn.ensemble import RandomForestRegressor
model = RandomForestRegressor()
model.fit(X, y)
```

### 3. 迁移学习
```python
# 作为源域数据进行跨数据集迁移
source_X, source_y = X, y
# 迁移到其他电池数据集
```

## 技术特点

1. **物理意义明确**: 每个特征都对应电池的物理特性
2. **数据完整性**: 覆盖完整的电池寿命周期
3. **标准化处理**: 特征提取过程标准化，便于复现
4. **多尺度信息**: 包含统计、时域、频域等多维度信息
5. **PINN兼容**: 特征设计考虑了物理约束的需求

## 文件结构

```
AHNU_Project/
├── data/
│   ├── cycledata.csv              # 原始循环数据
│   ├── AHNU_17_features.csv       # 提取的17维特征
│   ├── AHNU_features_X.npy        # 特征矩阵
│   ├── AHNU_features_y.npy        # SOH标签
│   └── AHNU_features_analysis.png # 数据分析图
├── extract_xjtu_features.py       # 主要特征提取脚本
├── feature_extraction_demo.py     # 演示和分析脚本
└── README.md                      # 本说明文档
```

## 依赖环境

```python
pandas >= 1.3.5
numpy >= 1.20.3
scipy >= 1.7.0
matplotlib >= 3.3.4
```

## 注意事项

1. **数据质量**: 原始数据包含55,592行，确保数据完整性
2. **内存使用**: 特征提取过程需要约100MB内存
3. **计算时间**: 完整提取约需1-2分钟
4. **中文编码**: CSV文件使用UTF-8编码处理中文列名
5. **数值精度**: 使用float64精度保证计算准确性

## 引用

如果使用本代码，请引用原始PINN4SOH论文：

```bibtex
@article{wang2024physics,
  title={Physics-informed neural network for lithium-ion battery degradation stable modeling and prognosis},
  author={Wang, Fujin and Zhai, Zhi and Zhao, Zhibin and Di, Yi and Chen, Xuefeng},
  journal={Nature Communications},
  volume={15},
  number={1},
  pages={4332},
  year={2024},
  publisher={Nature Publishing Group UK London}
}
```

---

**作者**: AI Assistant  
**日期**: 2025-01-27  
**版本**: 1.0