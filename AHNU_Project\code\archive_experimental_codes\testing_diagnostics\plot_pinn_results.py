#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制完整PINN模型的预测回归图
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import scienceplots
plt.style.use(['science', 'nature'])
import sys
import os
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler  # 修复导入问题

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from Model.Model import PINN as BasePINN
from utils.util import eval_metrix

device = 'cuda' if torch.cuda.is_available() else 'cpu'

class PINN(BasePINN):
    """修复的PINN模型，处理正确的维度"""
    
    def __init__(self, args):
        super(PINN, self).__init__(args)  # 调用父类初始化
        
        # 修复Solution_u，使其接受18维输入（17特征+1时间）
        # 重新定义Solution_u的encoder，使其接受18维输入
        from Model.Model import MLP, Predictor
        self.solution_u.encoder = MLP(input_dim=18, output_dim=32, layers_num=3, hidden_dim=60, droupout=0.2).to(device)
        
        # 修复dynamical_F，使其接受37维输入
        self.dynamical_F = MLP(input_dim=37, output_dim=1,
                               layers_num=args.F_layers_num,
                               hidden_dim=args.F_hidden_dim,
                               droupout=0.2).to(device)
        
        # 重新初始化优化器
        self.optimizer1 = torch.optim.Adam(self.solution_u.parameters(), lr=args.warmup_lr)
        self.optimizer2 = torch.optim.Adam(self.dynamical_F.parameters(), lr=args.lr_F)

class AHNUDataProcessor:
    """AHNU数据处理器 - 适配PINN模型"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.scaler = StandardScaler()
        self.nominal_capacity = 1200.0
        
    def load_and_process_data(self):
        """加载并处理AHNU数据，适配PINN格式"""
        print(f"📊 加载AHNU数据: {self.file_path}")
        
        # 读取Excel文件
        df = pd.read_excel(self.file_path)
        print(f"原始数据形状: {df.shape}")
        
        # 提取17维特征
        features_17d = self._extract_17_features(df)
        
        # 计算SOH标签
        soh_labels = self._calculate_soh(df)
        
        # 添加时间维度（PINN需要时间信息）
        time_steps = np.arange(len(features_17d)) / len(features_17d)  # 归一化时间 [0,1]
        
        # 组合特征和时间：[17维特征 + 1维时间] = 18维
        features_with_time = np.column_stack([features_17d, time_steps.reshape(-1, 1)])
        
        # 创建序列对用于PINN训练
        X1, X2, Y1, Y2 = self._create_sequence_pairs(features_with_time, soh_labels)
        
        return X1, X2, Y1, Y2
    
    def _extract_17_features(self, df):
        """从AHNU数据提取17维统计特征"""
        features = []
        
        # 选择关键的电池参数列进行特征提取
        key_columns = [
            '充电比容量(mAh/g)', '放电比容量(mAh/g)', '充放电效率(%)',
            '中值电压(V)', '充电时间(h)', '放电时间(h)', 
            '充电平均电压(V)', '放电平均电压(V)'
        ]
        
        for _, row in df.iterrows():
            row_features = []
            
            # 1-8: 各列的当前值
            for col in key_columns:
                row_features.append(row[col])
            
            # 9-10: 容量相关比率
            charge_cap = row['充电比容量(mAh/g)']
            discharge_cap = row['放电比容量(mAh/g)']
            row_features.append(charge_cap / self.nominal_capacity)
            row_features.append(discharge_cap / self.nominal_capacity)
            
            # 11-12: 电压差和时间比
            voltage_diff = row['充电平均电压(V)'] - row['放电平均电压(V)']
            time_ratio = row['充电时间(h)'] / (row['放电时间(h)'] + 1e-6)
            row_features.append(voltage_diff)
            row_features.append(time_ratio)
            
            # 13-14: 效率相关特征
            efficiency = row['充放电效率(%)']
            row_features.append(efficiency / 100.0)
            row_features.append(np.log(efficiency + 1))
            
            # 15-16: 循环相关特征
            cycle_num = row['循环号']
            row_features.append(cycle_num / 216.0)
            row_features.append(np.sqrt(cycle_num))
            
            # 17: 综合健康指标
            health_indicator = (discharge_cap * efficiency / 100.0) / self.nominal_capacity
            row_features.append(health_indicator)
            
            features.append(row_features)
        
        features_array = np.array(features)
        print(f"提取的17维特征形状: {features_array.shape}")
        
        # 标准化特征
        features_array = self.scaler.fit_transform(features_array)
        
        return features_array
    
    def _calculate_soh(self, df):
        """计算SOH值"""
        # 使用放电容量计算SOH
        discharge_capacity = df['放电比容量(mAh/g)'].values
        
        # 相对于初始容量的SOH
        initial_capacity = discharge_capacity[0]
        soh_values = discharge_capacity / initial_capacity
        
        # 确保SOH在合理范围内
        soh_values = np.clip(soh_values, 0.1, 1.2)
        
        print(f"SOH值范围: [{soh_values.min():.4f}, {soh_values.max():.4f}]")
        return soh_values.reshape(-1, 1)
    
    def _create_sequence_pairs(self, features, soh_labels):
        """创建序列对用于PINN训练"""
        n_samples = len(features)
        
        # 创建连续的样本对 (t, t+1)
        X1, X2 = [], []
        Y1, Y2 = [], []
        
        for i in range(n_samples - 1):
            X1.append(features[i])
            X2.append(features[i + 1]) 
            Y1.append(soh_labels[i])
            Y2.append(soh_labels[i + 1])
        
        return np.array(X1), np.array(X2), np.array(Y1), np.array(Y2)

def get_args():
    """获取PINN模型参数"""
    import argparse
    parser = argparse.ArgumentParser()
    
    # 数据相关
    parser.add_argument('--data', type=str, default='AHNU', help='数据集名称')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--normalization_method', type=str, default='z-score', help='标准化方法')
    
    # 训练相关
    parser.add_argument('--epochs', type=int, default=50, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-3, help='学习率')
    parser.add_argument('--warmup_epochs', type=int, default=10, help='预热轮数')
    parser.add_argument('--warmup_lr', type=float, default=5e-4, help='预热学习率')
    parser.add_argument('--final_lr', type=float, default=1e-4, help='最终学习率')
    parser.add_argument('--lr_F', type=float, default=1e-3, help='动力学网络学习率')
    parser.add_argument('--iter_per_epoch', type=int, default=1, help='每轮迭代数')
    
    # 模型架构
    parser.add_argument('--F_layers_num', type=int, default=3, help='动力学网络层数')
    parser.add_argument('--F_hidden_dim', type=int, default=32, help='动力学网络隐藏维度')
    
    # 损失权重
    parser.add_argument('--alpha', type=float, default=0.1, help='PDE损失权重')
    parser.add_argument('--beta', type=float, default=0.5, help='物理损失权重')
    
    # 早停和保存
    parser.add_argument('--early_stop', type=int, default=30, help='早停耐心值')
    parser.add_argument('--save_folder', type=str, default='./AHNU_Project/results', help='保存目录')
    parser.add_argument('--log_dir', type=str, default='pinn_full_test.log', help='日志文件')
    
    return parser.parse_args()

def plot_regression_results(true_labels, pred_labels, r2_score, save_path=None):
    """绘制回归结果图"""
    # 创建图形
    fig, ax = plt.subplots(figsize=(4, 3), dpi=200)
    
    # 计算误差
    errors = np.abs(pred_labels - true_labels)
    
    # 绘制散点图
    scatter = ax.scatter(true_labels, pred_labels, c=errors, cmap='viridis', s=20, alpha=0.7)
    
    # 绘制理想预测线
    min_val = min(true_labels.min(), pred_labels.min())
    max_val = max(true_labels.max(), pred_labels.max())
    ax.plot([min_val, max_val], [min_val, max_val], '--', c='#ff4d4e', alpha=1, linewidth=1)
    
    # 设置图形属性
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.set_title(f'PINN Model Prediction (R² = {r2_score:.4f})')
    ax.set_aspect('equal')
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax)
    cbar.set_label('Absolute Error')
    
    # 设置坐标轴范围
    ax.set_xlim([0.1, 1.2])
    ax.set_ylim([0.1, 1.2])
    
    plt.tight_layout()
    
    # 保存或显示图形
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"回归图已保存到: {save_path}")
    else:
        plt.show()
    
    plt.close()

def main():
    """主函数"""
    print("📊 绘制完整PINN模型预测回归图")
    
    # 获取参数
    args = get_args()
    
    # 数据文件路径
    data_file = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
    
    # 加载数据
    processor = AHNUDataProcessor(data_file)
    X1, X2, Y1, Y2 = processor.load_and_process_data()
    
    # 转换为张量
    X1 = torch.from_numpy(X1).float()
    X2 = torch.from_numpy(X2).float()
    Y1 = torch.from_numpy(Y1).float()
    Y2 = torch.from_numpy(Y2).float()
    
    # 划分数据集
    indices = torch.arange(len(X1))
    train_idx, temp_idx = train_test_split(indices, test_size=0.4, random_state=42)
    valid_idx, test_idx = train_test_split(temp_idx, test_size=0.5, random_state=42)
    
    # 创建数据加载器
    test_dataset = TensorDataset(X1[test_idx], X2[test_idx], Y1[test_idx], Y2[test_idx])
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)
    
    # 创建PINN模型
    model = PINN(args).to(device)
    
    # 加载训练好的模型权重
    results_folder = './AHNU_Project/results'
    model_path = os.path.join(results_folder, 'model.pth')
    
    if os.path.exists(model_path):
        print(f"📥 加载模型权重: {model_path}")
        checkpoint = torch.load(model_path, map_location=device)
        model.solution_u.load_state_dict(checkpoint['solution_u'])
        model.dynamical_F.load_state_dict(checkpoint['dynamical_F'])
    else:
        print(f"⚠️  未找到模型权重文件: {model_path}")
        print("请先运行训练脚本生成模型权重")
        return
    
    # 测试模型
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for iter, (x1, _, y1, _) in enumerate(test_loader):
            x1 = x1.to(device)
            u1 = model.predict(x1)
            all_predictions.append(u1.cpu().numpy())
            all_targets.append(y1.numpy())
    
    pred_labels = np.concatenate(all_predictions, axis=0)
    true_labels = np.concatenate(all_targets, axis=0)
    
    # 计算指标
    r2 = r2_score(true_labels.flatten(), pred_labels.flatten())
    
    print(f"📈 模型性能指标:")
    print(f"  - R²: {r2:.6f}")
    
    # 绘制回归图
    save_path = os.path.join(results_folder, 'pinn_regression_plot.png')
    plot_regression_results(true_labels, pred_labels, r2, save_path)
    
    print("✅ 回归图绘制完成!")

if __name__ == "__main__":
    main()