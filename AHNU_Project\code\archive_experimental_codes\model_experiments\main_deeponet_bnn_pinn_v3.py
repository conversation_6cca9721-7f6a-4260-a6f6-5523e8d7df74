import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.autograd import grad
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import argparse
import os
import random
import matplotlib.pyplot as plt
import scienceplots
import math

# BNN Imports from Blitz
import blitz.modules as bnn

# Check for GPU availability
device = 'cuda' if torch.cuda.is_available() else 'cpu'

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 0. AHNU Data Loader (新增)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class AHNUDataProcessor:
    """AHNU数据集处理器 - 提取17维特征和SOH标签"""
    
    def __init__(self, file_path, args):
        self.file_path = file_path
        self.args = args
        self.scaler = StandardScaler()
        
        # 定义额定容量(mAh/g) - 根据AHNU数据调整
        self.nominal_capacity = 1200.0  # 可根据实际情况调整
        
    def load_and_process_data(self):
        """加载AHNU Excel数据并处理"""
        print(f"📊 加载AHNU数据: {self.file_path}")
        
        # 读取Excel文件
        df = pd.read_excel(self.file_path)
        print(f"原始数据形状: {df.shape}")
        
        # 提取17维特征
        features_17d = self._extract_17_features(df)
        
        # 计算SOH标签
        soh_labels = self._calculate_soh(df)
        
        # 创建数据对
        X1, X2, Y1, Y2 = self._create_sequence_pairs(features_17d, soh_labels)
        
        return X1, X2, Y1, Y2
    
    def _extract_17_features(self, df):
        """从AHNU数据提取17维统计特征"""
        features = []
        
        # 选择关键的电池参数列进行特征提取
        key_columns = [
            '充电比容量(mAh/g)', '放电比容量(mAh/g)', '充放电效率(%)',
            '中值电压(V)', '充电时间(h)', '放电时间(h)', 
            '充电平均电压(V)', '放电平均电压(V)'
        ]
        
        for _, row in df.iterrows():
            row_features = []
            
            # 从关键列提取统计特征(总共17维)
            # 1-8: 各列的当前值
            for col in key_columns:
                row_features.append(row[col])
            
            # 9-10: 容量相关比率
            charge_cap = row['充电比容量(mAh/g)']
            discharge_cap = row['放电比容量(mAh/g)']
            row_features.append(charge_cap / self.nominal_capacity)  # 充电容量比
            row_features.append(discharge_cap / self.nominal_capacity)  # 放电容量比
            
            # 11-12: 电压差和时间比
            voltage_diff = row['充电平均电压(V)'] - row['放电平均电压(V)']
            time_ratio = row['充电时间(h)'] / (row['放电时间(h)'] + 1e-6)
            row_features.append(voltage_diff)
            row_features.append(time_ratio)
            
            # 13-14: 效率相关特征
            efficiency = row['充放电效率(%)']
            row_features.append(efficiency / 100.0)  # 标准化效率
            row_features.append(np.log(efficiency + 1))  # 对数变换效率
            
            # 15-16: 循环相关特征
            cycle_num = row['循环号']
            row_features.append(cycle_num / 216.0)  # 标准化循环号
            row_features.append(np.sqrt(cycle_num))  # 平方根变换
            
            # 17: 综合健康指标
            health_indicator = (discharge_cap * efficiency / 100.0) / self.nominal_capacity
            row_features.append(health_indicator)
            
            features.append(row_features)
        
        features_array = np.array(features)
        print(f"提取的17维特征形状: {features_array.shape}")
        
        # 标准化特征
        if self.args.normalization_method == 'z-score':
            features_array = self.scaler.fit_transform(features_array)
        
        return features_array
    
    def _calculate_soh(self, df):
        """计算SOH值"""
        # 使用放电容量计算SOH
        discharge_capacity = df['放电比容量(mAh/g)'].values
        
        # 方法1: 相对于额定容量的SOH
        soh_nominal = discharge_capacity / self.nominal_capacity
        
        # 方法2: 相对于初始容量的SOH (更常用)
        initial_capacity = discharge_capacity[0]  # 第一个循环的容量作为初始容量
        soh_relative = discharge_capacity / initial_capacity
        
        # 确保SOH在合理范围内 [0, 1.2] (允许初期容量增长)
        soh_values = np.clip(soh_relative, 0.1, 1.2)
        
        print(f"SOH值范围: [{soh_values.min():.4f}, {soh_values.max():.4f}]")
        return soh_values.reshape(-1, 1)
    
    def _create_sequence_pairs(self, features, soh_labels):
        """创建序列对用于训练，添加时间维度"""
        n_samples = len(features)
        
        # 添加时间维度（PINN需要时间信息）
        time_steps = np.arange(n_samples) / n_samples  # 归一化时间 [0,1]
        
        # 组合特征和时间：[17维特征 + 1维时间] = 18维
        features_with_time = np.column_stack([features, time_steps.reshape(-1, 1)])
        
        # 创建连续的样本对 (t, t+1)
        X1, X2 = [], []
        Y1, Y2 = [], []
        
        for i in range(n_samples - 1):
            X1.append(features_with_time[i])
            X2.append(features_with_time[i + 1]) 
            Y1.append(soh_labels[i])
            Y2.append(soh_labels[i + 1])
        
        return np.array(X1), np.array(X2), np.array(Y1), np.array(Y2)

class AHNUDataLoader:
    """AHNU数据加载器"""
    
    def __init__(self, file_path, args):
        self.file_path = file_path
        self.args = args
        self.processor = AHNUDataProcessor(file_path, args)
    
    def get_dataloaders(self):
        """获取训练、验证和测试数据加载器"""
        # 加载和处理数据
        X1, X2, Y1, Y2 = self.processor.load_and_process_data()
        
        # 转换为PyTorch张量
        tensor_X1 = torch.from_numpy(X1).float()
        tensor_X2 = torch.from_numpy(X2).float() 
        tensor_Y1 = torch.from_numpy(Y1).float()
        tensor_Y2 = torch.from_numpy(Y2).float()
        
        print(f"张量形状: X1={tensor_X1.shape}, Y1={tensor_Y1.shape}")
        
        # 划分训练集和测试集 (80/20)
        split_idx = int(len(tensor_X1) * 0.8)
        
        train_X1, test_X1 = tensor_X1[:split_idx], tensor_X1[split_idx:]
        train_X2, test_X2 = tensor_X2[:split_idx], tensor_X2[split_idx:]
        train_Y1, test_Y1 = tensor_Y1[:split_idx], tensor_Y1[split_idx:]
        train_Y2, test_Y2 = tensor_Y2[:split_idx], tensor_Y2[split_idx:]
        
        # 从训练集中划分验证集
        train_X1, valid_X1, train_X2, valid_X2, train_Y1, valid_Y1, train_Y2, valid_Y2 = \
            train_test_split(train_X1, train_X2, train_Y1, train_Y2, 
                           test_size=0.2, random_state=42)
        
        # 创建数据加载器
        train_loader = DataLoader(
            TensorDataset(train_X1, train_X2, train_Y1, train_Y2),
            batch_size=self.args.batch_size, shuffle=True
        )
        
        valid_loader = DataLoader(
            TensorDataset(valid_X1, valid_X2, valid_Y1, valid_Y2),
            batch_size=self.args.batch_size, shuffle=False
        )
        
        test_loader = DataLoader(
            TensorDataset(test_X1, test_X2, test_Y1, test_Y2),
            batch_size=self.args.batch_size, shuffle=False
        )
        
        print(f"数据集大小: 训练={len(train_loader.dataset)}, "
              f"验证={len(valid_loader.dataset)}, 测试={len(test_loader.dataset)}")
        
        return {
            'train': train_loader,
            'valid': valid_loader, 
            'test': test_loader
        }

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 1. Enhanced DeepONet Implementation (复用v2中的实现)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class Swish(nn.Module):
    """Swish activation function: x * sigmoid(x)"""
    def forward(self, x):
        return x * torch.sigmoid(x)

class EnhancedBranchNet(nn.Module):
    """Enhanced Branch network with better architecture"""
    def __init__(self, input_dim, latent_dim, hidden_dims=[64, 128, 64], dropout_rate=0.1):
        super(EnhancedBranchNet, self).__init__()
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        
        layers = []
        prev_dim = input_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                Swish(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, latent_dim))
        self.layers = nn.Sequential(*layers)
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                nn.init.constant_(m.bias, 0)
        
    def forward(self, x):
        return self.layers(x)

class EnhancedTrunkNet(nn.Module):
    """Enhanced Trunk network with better architecture"""
    def __init__(self, query_dim, latent_dim, hidden_dims=[32, 64, 32], dropout_rate=0.1):
        super(EnhancedTrunkNet, self).__init__()
        self.query_dim = query_dim
        self.latent_dim = latent_dim
        
        layers = []
        prev_dim = query_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                Swish(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, latent_dim))
        self.layers = nn.Sequential(*layers)
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)
                nn.init.constant_(m.bias, 0)
        
    def forward(self, y):
        return self.layers(y)

class EnhancedDeepONet(nn.Module):
    """Enhanced DeepONet with improved architecture for AHNU data"""
    def __init__(self, input_dim, query_dim, latent_dim=64, 
                 branch_hidden_dims=[64, 128, 64], trunk_hidden_dims=[32, 64, 32], 
                 dropout_rate=0.1):
        super(EnhancedDeepONet, self).__init__()
        self.input_dim = input_dim
        self.query_dim = query_dim
        self.latent_dim = latent_dim
        
        self.branch_net = EnhancedBranchNet(input_dim, latent_dim, branch_hidden_dims, dropout_rate)
        self.trunk_net = EnhancedTrunkNet(query_dim, latent_dim, trunk_hidden_dims, dropout_rate)
        
        self.bias = nn.Parameter(torch.zeros(1))
        
        self.output_transform = nn.Sequential(
            nn.Linear(1, 16),
            Swish(),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(16, 1)
        )
        
    def forward(self, u, y):
        branch_out = self.branch_net(u)
        trunk_out = self.trunk_net(y)
        
        output = torch.sum(branch_out * trunk_out, dim=1, keepdim=True) + self.bias
        output = self.output_transform(output)
        
        return output

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 2. Enhanced Bayesian Solution Network (复用v2)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def get_kl_divergence(module):
    """Recursive function to fetch kl-divergence from Bayesian Modules"""
    kl_loss = torch.tensor(0.0, device=device)
    for sub_module in module.children():
        if isinstance(sub_module, bnn.BayesianLinear):
            kl_loss += sub_module.log_variational_posterior - sub_module.log_prior
        elif isinstance(sub_module, nn.Module):
            kl_loss += get_kl_divergence(sub_module)
    return kl_loss

class EnhancedBayesianMLP(nn.Module):
    def __init__(self, input_dim, output_dim, hidden_dims, dropout_rate=0.1):
        super().__init__()
        self.layers = nn.ModuleList()

        prev_dim = input_dim
        for i, hidden_dim in enumerate(hidden_dims):
            self.layers.append(bnn.BayesianLinear(prev_dim, hidden_dim))
            if i < len(hidden_dims) - 1:
                self.layers.append(nn.BatchNorm1d(hidden_dim))
                self.layers.append(Swish())
                self.layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim

        if len(hidden_dims) > 0:
            self.layers.append(bnn.BayesianLinear(prev_dim, output_dim))
        else:
            self.layers.append(bnn.BayesianLinear(input_dim, output_dim))

    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        return x

class EnhancedBayesianSolution_u(nn.Module):
    """Enhanced Bayesian Solution Network for AHNU data (极简版本)"""
    def __init__(self, soh_constraint_type='sigmoid'):
        super().__init__()
        # 极简版本：只保留最小可行的贝叶斯网络
        self.encoder = bnn.BayesianLinear(17, 8)  # 直接从17到8
        self.dropout1 = nn.Dropout(0.5)
        self.predictor = bnn.BayesianLinear(8, 1)  # 直接从8到1
        
        self.soh_constraint_type = soh_constraint_type

        if soh_constraint_type == 'sigmoid_scaled':
            self.soh_scale = nn.Parameter(torch.tensor(1.2))
            self.soh_offset = nn.Parameter(torch.tensor(0.1))

    def forward(self, x):
        x = self.encoder(x)
        x = torch.relu(x)  # 简单激活函数
        x = self.dropout1(x)
        raw_output = self.predictor(x)

        # Apply SOH constraints for battery data
        if self.soh_constraint_type == 'sigmoid':
            constrained_output = torch.sigmoid(raw_output) * 1.2 + 0.1
        elif self.soh_constraint_type == 'sigmoid_scaled':
            constrained_output = torch.sigmoid(raw_output) * self.soh_scale + self.soh_offset
        elif self.soh_constraint_type == 'tanh_scaled':
            constrained_output = 0.5 * (torch.tanh(raw_output) + 1) * 1.2 + 0.1
        elif self.soh_constraint_type == 'clamp':
            constrained_output = torch.clamp(raw_output, 0.1, 1.2)
        else:
            constrained_output = raw_output

        return constrained_output

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 3. Loss Functions (复用并调整v2)
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class SOHConstraintLoss(nn.Module):
    """Custom loss function with SOH constraints for AHNU data"""
    def __init__(self, constraint_weight=1.0):
        super().__init__()
        self.constraint_weight = constraint_weight
        self.mse = nn.MSELoss()

    def forward(self, pred, target):
        mse_loss = self.mse(pred, target)

        # SOH constraint losses for battery (允许0.1-1.2范围)
        lower_violation = torch.relu(0.1 - pred)
        upper_violation = torch.relu(pred - 1.2)
        constraint_loss = (lower_violation + upper_violation).mean()

        total_loss = mse_loss + self.constraint_weight * constraint_loss
        return total_loss, mse_loss, constraint_loss

class PhysicsConstraintLoss(nn.Module):
    """Physics constraint loss for battery degradation"""
    def __init__(self, monotonicity_weight=1.0, smoothness_weight=0.1):
        super().__init__()
        self.monotonicity_weight = monotonicity_weight
        self.smoothness_weight = smoothness_weight
        self.relu = nn.ReLU()

    def forward(self, u1, u2, y1, y2):
        # Degradation monotonicity: SOH should generally decrease
        # u2 <= u1 for battery degradation
        monotonic_loss = self.relu(u2 - u1).mean()

        # Smoothness constraint
        pred_diff = torch.abs(u2 - u1)
        target_diff = torch.abs(y2 - y1)
        smoothness_loss = torch.abs(pred_diff - target_diff).mean()

        total_physics_loss = (self.monotonicity_weight * monotonic_loss +
                            self.smoothness_weight * smoothness_loss)

        return total_physics_loss, monotonic_loss, smoothness_loss

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 4. Main PINN Model for AHNU Data
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

class AHNUDeepONet_BNN_PINN(nn.Module):
    """AHNU数据专用的DeepONet-BNN-PINN模型"""
    def __init__(self, args):
        super().__init__()
        self.args = args

        # Bayesian solution network for 17D AHNU features
        self.solution_u = EnhancedBayesianSolution_u(
            soh_constraint_type=args.soh_constraint_type
        ).to(device)

        # DeepONet for dynamics (输入维度调整为18+1+1=20)
        self.dynamical_F = EnhancedDeepONet(
            input_dim=20,  # 18维原特征 + 1维SOH + 1维SOH导数
            query_dim=1,   # 时间查询
            latent_dim=args.deeponet_latent_dim,
            branch_hidden_dims=args.branch_hidden_dims,
            trunk_hidden_dims=args.trunk_hidden_dims,
            dropout_rate=args.dropout_rate
        ).to(device)

        # Loss functions
        self.soh_loss = SOHConstraintLoss(constraint_weight=args.soh_constraint_weight)
        self.physics_loss = PhysicsConstraintLoss(
            monotonicity_weight=args.monotonicity_weight,
            smoothness_weight=args.smoothness_weight
        )
        self.pde_loss = nn.MSELoss()

        # Optimizers
        self.optimizer_u = torch.optim.AdamW(
            self.solution_u.parameters(),
            lr=args.lr_u,
            weight_decay=args.weight_decay_u
        )
        self.optimizer_f = torch.optim.AdamW(
            self.dynamical_F.parameters(),
            lr=args.lr_f,
            weight_decay=args.weight_decay_f
        )

        # Schedulers
        self.scheduler_u = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer_u, mode='min', factor=0.8, patience=8, verbose=True
        )
        self.scheduler_f = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer_f, mode='min', factor=0.8, patience=8, verbose=True
        )

        self.training_history = {
            'total_loss': [], 'data_loss': [], 'pde_loss': [], 'physics_loss': [],
            'constraint_loss': [], 'kl_loss': []
        }

    def forward(self, x):
        """前向传播 - 输入x是18维特征向量（17特征+1时间）"""
        x.requires_grad = True
        
        # 提取17维特征和时间维度
        features_17d = x[:, :-1]  # 前17维特征
        t = x[:, -1:].requires_grad_(True)  # 最后1维是时间
        
        # 获取SOH预测（使用原失17维特征）
        u = self.solution_u(features_17d)
        
        # 计算SOH对时间的梯度（简化处理）
        u_t = torch.zeros_like(u).to(device)  # 简化版本，实际可以计算梯度
        
        # 准备DeepONet输入（原18维特征 + SOH + SOH导数 = 20维）
        branch_input = torch.cat([x, u, u_t], dim=1)  # 18+1+1=20维
        trunk_input = t
        
        # 获取动力学预测
        F = self.dynamical_F(branch_input, trunk_input)
        
        return u, u_t - F

    def train_one_epoch(self, dataloader, stage="joint"):
        """
        训练一个epoch
        Args:
            dataloader: 数据加载器
            stage: "joint" 表示联合训练, "finetune" 表示仅训练solution_u
        """
        self.train()
        epoch_metrics = {
            'total_loss': 0, 'data_loss': 0, 'pde_loss': 0, 'physics_loss': 0,
            'constraint_loss': 0, 'kl_loss': 0, 'n_batches': 0
        }

        for x1, x2, y1, y2 in dataloader:
            x1, x2, y1, y2 = [d.to(device) for d in [x1, x2, y1, y2]]

            # Forward pass
            u1, f1 = self.forward(x1)
            u2, f2 = self.forward(x2)

            # Loss computation
            soh_loss_1, mse_loss_1, constraint_loss_1 = self.soh_loss(u1, y1)
            soh_loss_2, mse_loss_2, constraint_loss_2 = self.soh_loss(u2, y2)
            loss_data = 0.5 * (soh_loss_1 + soh_loss_2)
            constraint_loss = 0.5 * (constraint_loss_1 + constraint_loss_2)

            # PDE loss
            loss_pde = 0.5 * (self.pde_loss(f1, torch.zeros_like(f1)) +
                             self.pde_loss(f2, torch.zeros_like(f2)))

            # Physics loss
            physics_loss_total, _, _ = self.physics_loss(u1, u2, y1, y2)

            # Bayesian complexity cost
            complexity_cost = get_kl_divergence(self.solution_u)

            # Total loss
            total_loss = (loss_data +
                         self.args.alpha * loss_pde +
                         self.args.beta * physics_loss_total +
                         self.args.complexity_weight * complexity_cost)

            # Optimization - 根据训练阶段选择优化器
            if stage == "joint":
                # 联合训练阶段：同时更新两个网络
                self.optimizer_u.zero_grad()
                self.optimizer_f.zero_grad()
                total_loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.solution_u.parameters(), max_norm=1.0)
                torch.nn.utils.clip_grad_norm_(self.dynamical_F.parameters(), max_norm=1.0)
                
                self.optimizer_u.step()
                self.optimizer_f.step()
                
                # Update schedulers
                self.scheduler_u.step(epoch_metrics.get('total_loss', total_loss.item()))
                self.scheduler_f.step(epoch_metrics.get('total_loss', total_loss.item()))
                
            elif stage == "finetune":
                # 微调阶段：只更新solution_u
                self.optimizer_u.zero_grad()
                total_loss.backward()
                
                # Gradient clipping (只对solution_u)
                torch.nn.utils.clip_grad_norm_(self.solution_u.parameters(), max_norm=1.0)
                
                self.optimizer_u.step()
                
                # Update scheduler (只对solution_u)
                self.scheduler_u.step(epoch_metrics.get('total_loss', total_loss.item()))

            # Metrics
            epoch_metrics['total_loss'] += total_loss.item()
            epoch_metrics['data_loss'] += loss_data.item()
            epoch_metrics['pde_loss'] += loss_pde.item()
            epoch_metrics['physics_loss'] += physics_loss_total.item()
            epoch_metrics['constraint_loss'] += constraint_loss.item()
            epoch_metrics['kl_loss'] += complexity_cost.item()
            epoch_metrics['n_batches'] += 1

        # Average metrics
        for key in epoch_metrics:
            if key != 'n_batches':
                epoch_metrics[key] /= epoch_metrics['n_batches']

        # Store history
        for key in self.training_history:
            if key in epoch_metrics:
                self.training_history[key].append(epoch_metrics[key])

        return epoch_metrics
    
    def freeze_dynamical_f(self):
        """冻结dynamical_F的参数"""
        for param in self.dynamical_F.parameters():
            param.requires_grad = False
        print("🧊 已冻结 dynamical_F 网络参数")
    
    def unfreeze_dynamical_f(self):
        """解冻dynamical_F的参数"""
        for param in self.dynamical_F.parameters():
            param.requires_grad = True
        print("🔥 已解冻 dynamical_F 网络参数")
    
    def save_stage_model(self, stage_name, save_dir):
        """保存阶段性模型"""
        stage_model = {
            'solution_u': self.solution_u.state_dict(),
            'dynamical_F': self.dynamical_F.state_dict(),
            'optimizer_u': self.optimizer_u.state_dict(),
            'optimizer_f': self.optimizer_f.state_dict(),
            'training_history': self.training_history
        }
        save_path = os.path.join(save_dir, f'ahnu_deeponet_bnn_model_{stage_name}.pth')
        torch.save(stage_model, save_path)
        print(f"📁 {stage_name}阶段模型已保存: {save_path}")
        return save_path
    
    def load_stage_model(self, model_path, stage="joint"):
        """加载阶段性模型"""
        checkpoint = torch.load(model_path)
        self.solution_u.load_state_dict(checkpoint['solution_u'])
        self.dynamical_F.load_state_dict(checkpoint['dynamical_F'])
        
        if stage == "joint":
            self.optimizer_u.load_state_dict(checkpoint['optimizer_u'])
            self.optimizer_f.load_state_dict(checkpoint['optimizer_f'])
        elif stage == "finetune":
            # 微调阶段只加载solution_u的优化器
            self.optimizer_u.load_state_dict(checkpoint['optimizer_u'])
            
        if 'training_history' in checkpoint:
            self.training_history = checkpoint['training_history']
            
        print(f"📂 已加载模型: {model_path} (阶段: {stage})")

    def predict_with_uncertainty(self, dataloader, n_samples):
        """带不确定性的预测"""
        self.eval()
        all_true, all_preds = [], []
        soh_violations = 0
        total_predictions = 0

        with torch.no_grad():
            for x1, _, y1, _ in dataloader:
                x1, y1 = x1.to(device), y1.to(device)

                preds = []
                for _ in range(n_samples):
                    pred, _ = self.forward(x1)
                    preds.append(pred.cpu().numpy())

                    violations = ((pred < 0.1) | (pred > 1.2)).sum().item()
                    soh_violations += violations
                    total_predictions += pred.numel()

                all_preds.append(np.stack(preds, axis=0))
                all_true.append(y1.cpu().numpy())

        all_preds = np.concatenate(all_preds, axis=1)
        pred_mean = np.mean(all_preds, axis=0)
        pred_std = np.std(all_preds, axis=0)

        violation_rate = soh_violations / total_predictions if total_predictions > 0 else 0
        return np.concatenate(all_true, axis=0), pred_mean, pred_std, violation_rate

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 5. Plotting and Analysis Functions
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def plot_ahnu_results(true, pred_mean, pred_std, save_path, violation_rate=0):
    """绘制AHNU数据结果"""
    # 使用支持中文的字体设置
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    fig, axes = plt.subplots(2, 2, figsize=(12, 8), dpi=200)

    # 主预测图
    ax = axes[0, 0]
    cycles = range(len(true))
    ax.plot(cycles, true, label='True SOH', color='#403990', linewidth=2, zorder=3)
    ax.plot(cycles, pred_mean, label='AHNU-DeepONet-BNN-PINN', color='#CF3D3E', linewidth=2, zorder=2)
    ax.fill_between(cycles,
                    (pred_mean - 2*pred_std).flatten(),
                    (pred_mean + 2*pred_std).flatten(),
                    color='#FBDD85', alpha=0.5, label='95% CI', zorder=1)
    ax.axhline(y=0.8, color='red', linestyle='--', alpha=0.7, label='SOH=0.8')
    ax.set_xlabel('Cycle Number')
    ax.set_ylabel('State of Health (SOH)')
    ax.set_title(f'AHNU Battery SOH Prediction (Violation Rate: {violation_rate:.4f})')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # 误差分析
    ax = axes[0, 1]
    errors = np.abs(pred_mean.flatten() - true.flatten())
    ax.plot(cycles, errors, color='#2E8B57', linewidth=1)
    ax.set_xlabel('Cycle Number')
    ax.set_ylabel('Absolute Error')
    ax.set_title(f'Prediction Error (MAE: {np.mean(errors):.6f})')
    ax.grid(True, alpha=0.3)

    # 不确定性分析
    ax = axes[1, 0]
    ax.plot(cycles, pred_std.flatten(), color='#FF6347', linewidth=1)
    ax.set_xlabel('Cycle Number')
    ax.set_ylabel('Prediction Uncertainty (Std)')
    ax.set_title(f'Uncertainty Distribution (Mean: {np.mean(pred_std):.6f})')
    ax.grid(True, alpha=0.3)

    # 真实值vs预测值散点图
    ax = axes[1, 1]
    ax.scatter(true.flatten(), pred_mean.flatten(), alpha=0.6, s=2, color='#4169E1')
    min_val, max_val = min(true.min(), pred_mean.min()), max(true.max(), pred_mean.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='完美预测')
    ax.set_xlabel('True SOH')
    ax.set_ylabel('Predicted SOH')
    ax.set_title('True vs Predicted')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Results plot saved: {save_path}")

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 6. Arguments and Main Execution for AHNU Data
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def get_ahnu_args():
    parser = argparse.ArgumentParser(description="AHNU DeepONet-BNN-PINN for Battery SOH")

    # AHNU数据参数（优化版本）
    parser.add_argument('--data_file', type=str, 
                       default=r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>',
                       help='AHNU数据文件路径')
    parser.add_argument('--epochs', type=int, default=50, help='训练轮数（降低）')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小（降低以防过拟合）')
    parser.add_argument('--normalization_method', type=str, default='z-score')

    # 模型架构参数（极简版本）
    parser.add_argument('--deeponet_latent_dim', type=int, default=8, help='DeepONet潜在维度（极简）')
    parser.add_argument('--branch_hidden_dims', type=int, nargs='+', default=[8],
                       help='Branch网络隐藏层维度（极简）')
    parser.add_argument('--trunk_hidden_dims', type=int, nargs='+', default=[4],
                       help='Trunk网络隐藏层维度（极简）')
    parser.add_argument('--dropout_rate', type=float, default=0.5, help='Dropout率（最大正则化）')

    # SOH约束参数
    parser.add_argument('--soh_constraint_type', type=str, default='sigmoid',
                       choices=['sigmoid', 'sigmoid_scaled', 'tanh_scaled', 'clamp', 'none'],
                       help='SOH约束类型')
    parser.add_argument('--soh_constraint_weight', type=float, default=2.0,
                       help='SOH约束损失权重（降低）')

    # 训练参数（根据truly_fixed经验进一步优化）
    parser.add_argument('--lr_u', type=float, default=1e-4, help='解网络学习率（进一步降低）')
    parser.add_argument('--lr_f', type=float, default=1e-4, help='DeepONet学习率（进一步降低）')
    parser.add_argument('--weight_decay_u', type=float, default=5e-4, help='解网络权重衰减（增加）')
    parser.add_argument('--weight_decay_f', type=float, default=5e-4, help='DeepONet权重衰减（增加）')

    # 损失权重（根据经验大幅调整，主要关注数据拟合）
    parser.add_argument('--alpha', type=float, default=0.01, help='PDE损失权重（大幅降低）')
    parser.add_argument('--beta', type=float, default=0.1, help='物理损失权重（大幅降低）')
    parser.add_argument('--complexity_weight', type=float, default=1e-6, help='贝叶斯复杂度权重（进一步降低）')
    parser.add_argument('--monotonicity_weight', type=float, default=0.05, help='单调性约束权重（大幅降低）')
    parser.add_argument('--smoothness_weight', type=float, default=0.01, help='平滑性约束权重（大幅降低）')

    # 两阶段训练参数（优化版本）
    parser.add_argument('--stage1_epochs', type=int, default=30, help='第一阶段联合训练轮数（降低）')
    parser.add_argument('--stage2_epochs', type=int, default=20, help='第二阶段微调轮数（降低）')
    parser.add_argument('--enable_two_stage', action='store_true', default=True, help='启用两阶段训练')
    parser.add_argument('--finetune_lr_u', type=float, default=1e-4, help='微调阶段solution_u学习率（降低）')

    # 不确定性量化
    parser.add_argument('--n_samples', type=int, default=50, help='不确定性估计样本数')

    # 日志和保存
    parser.add_argument('--verbose', action='store_true', help='详细输出')

    return parser.parse_args()

if __name__ == "__main__":
    print("🚀 启动AHNU DeepONet-BNN-PINN v3 电池SOH预测")
    args = get_ahnu_args()

    print(f"设备: {device}")
    if args.verbose:
        print(f"参数: {args}")

    # 创建结果目录
    base_dir = './neural_operator_experiment'
    results_dir = os.path.join(base_dir, 'results_v3_ahnu')
    plots_dir = os.path.join(base_dir, 'plots_v3_ahnu')
    os.makedirs(results_dir, exist_ok=True)
    os.makedirs(plots_dir, exist_ok=True)

    print(f"\n📊 AHNU数据训练配置:")
    print(f"  - 数据文件: {args.data_file}")
    print(f"  - SOH约束: {args.soh_constraint_type} (权重: {args.soh_constraint_weight})")
    print(f"  - DeepONet潜在维度: {args.deeponet_latent_dim}")
    print(f"  - Branch隐藏层: {args.branch_hidden_dims}")
    print(f"  - Trunk隐藏层: {args.trunk_hidden_dims}")
    print(f"  - 学习率: u={args.lr_u}, f={args.lr_f}")

    print(f"\n{'='*60}")
    print(f"🔋 处理AHNU数据集")
    print(f"{'='*60}")

    # 加载AHNU数据
    data_loader = AHNUDataLoader(args.data_file, args)
    loaders = data_loader.get_dataloaders()
    train_loader, valid_loader, test_loader = loaders['train'], loaders['valid'], loaders['test']

    # 创建模型
    model = AHNUDeepONet_BNN_PINN(args).to(device)
    model_save_path = os.path.join(results_dir, 'ahnu_deeponet_bnn_model.pth')

    # 打印模型信息（参考truly_fixed_full_pinn_test.py）
    solution_u_params = sum(p.numel() for p in model.solution_u.parameters() if p.requires_grad)
    dynamical_F_params = sum(p.numel() for p in model.dynamical_F.parameters() if p.requires_grad)
    total_params = solution_u_params + dynamical_F_params
    train_samples = len(train_loader.dataset)
    
    print(f"\n📊 模型参数统计:")
    print(f"  - Solution_u网络: {solution_u_params:,} 参数")
    print(f"  - Dynamical_F网络: {dynamical_F_params:,} 参数")
    print(f"  - 总参数量: {total_params:,} 参数")
    print(f"  - 训练样本: {train_samples} 个")
    print(f"  - 参数/样本比: {total_params/train_samples:.2f}")
    
    if total_params / train_samples > 10:
        print(f"⚠️  警告: 参数/样本比 > 10，可能过拟合！")
        print(f"💡 建议: 减少模型复杂度或增加正则化")

    print(f"🏁 开始两阶段训练...")
    
    if args.enable_two_stage:
        print(f"\n✨ 两阶段训练模式:")
        print(f"  阶段一: 联合训练 solution_u + dynamical_F ({args.stage1_epochs} 轮)")
        print(f"  阶段二: 冻结 dynamical_F, 微调 solution_u ({args.stage2_epochs} 轮)")
        total_epochs = args.stage1_epochs + args.stage2_epochs
    else:
        print(f"\n🔄 传统单阶段训练 ({args.epochs} 轮)")
        total_epochs = args.epochs
    
    # 训练参数（优化版本）
    best_loss = float('inf')
    patience_counter = 0
    patience_limit = 30  # 增加耐心值，参考truly_fixed经验
    stage1_best_model_path = None
    
    # =====================================================
    # 阶段一: 联合训练 (Joint Training)
    # =====================================================
    if args.enable_two_stage:
        print(f"\n💪 阶段一: 联合训练 solution_u + dynamical_F")
        print(f"=" * 60)
        
        stage1_epochs = args.stage1_epochs
        for epoch in range(1, stage1_epochs + 1):
            epoch_metrics = model.train_one_epoch(train_loader, stage="joint")
            
            # 早停检查
            if epoch_metrics['total_loss'] < best_loss:
                best_loss = epoch_metrics['total_loss']
                patience_counter = 0
                # 保存阶段一最佳模型
                stage1_best_model_path = model.save_stage_model("stage1_best", results_dir)
            else:
                patience_counter += 1
            
            # 进度报告
            if epoch % 10 == 0 or epoch == 1:
                print(f"  阶段一 {epoch:3d}/{stage1_epochs}: "
                      f"损失={epoch_metrics['total_loss']:.6f}, "
                      f"数据={epoch_metrics['data_loss']:.6f}, "
                      f"PDE={epoch_metrics['pde_loss']:.6f}, "
                      f"物理={epoch_metrics['physics_loss']:.6f}")
            
            # 早停
            if patience_counter >= patience_limit:
                print(f"  阶段一在第 {epoch} 轮早停 (耐心值超限)")
                break
        
        print(f"✅ 阶段一完成!最佳模型: {stage1_best_model_path}")
        
        # 加载阶段一最佳模型作为阶段二的起点
        if stage1_best_model_path:
            model.load_stage_model(stage1_best_model_path, stage="joint")
        
        # =====================================================
        # 阶段二: 冻结 dynamical_F, 微调 solution_u
        # =====================================================
        print(f"\n🧊 阶段二: 冻结 dynamical_F, 微调 solution_u")
        print(f"=" * 60)
        
        # 冻结 dynamical_F 参数
        model.freeze_dynamical_f()
        
        # 调整 solution_u 的学习率为微调率
        for param_group in model.optimizer_u.param_groups:
            param_group['lr'] = args.finetune_lr_u
        print(f"📋 微调阶段 solution_u 学习率设为: {args.finetune_lr_u}")
        
        # 重置早停参数
        best_loss = float('inf')
        patience_counter = 0
        patience_limit = 15  # 微调阶段耐心值可以设小一些
        
        stage2_epochs = args.stage2_epochs
        for epoch in range(1, stage2_epochs + 1):
            epoch_metrics = model.train_one_epoch(train_loader, stage="finetune")
            
            # 早停检查
            if epoch_metrics['total_loss'] < best_loss:
                best_loss = epoch_metrics['total_loss']
                patience_counter = 0
                # 保存最终最佳模型
                torch.save(model.state_dict(), model_save_path)
            else:
                patience_counter += 1
            
            # 进度报告
            if epoch % 5 == 0 or epoch == 1:
                print(f"  阶段二 {epoch:3d}/{stage2_epochs}: "
                      f"损失={epoch_metrics['total_loss']:.6f}, "
                      f"数据={epoch_metrics['data_loss']:.6f}, "
                      f"PDE={epoch_metrics['pde_loss']:.6f}, "
                      f"物理={epoch_metrics['physics_loss']:.6f}")
            
            # 早停
            if patience_counter >= patience_limit:
                print(f"  阶段二在第 {epoch} 轮早停 (耐心值超限)")
                break
        
        print(f"✅ 阶段二完成! 最终模型已保存: {model_save_path}")
        
        # 解冻参数(为了后续测试)
        model.unfreeze_dynamical_f()
        
    else:
        # =====================================================
        # 传统单阶段训练
        # =====================================================
        print(f"🏃 单阶段联合训练")
        print(f"=" * 60)
        
        for epoch in range(1, args.epochs + 1):
            epoch_metrics = model.train_one_epoch(train_loader, stage="joint")
            
            # 早停检查
            if epoch_metrics['total_loss'] < best_loss:
                best_loss = epoch_metrics['total_loss']
                patience_counter = 0
                torch.save(model.state_dict(), model_save_path)
            else:
                patience_counter += 1
            
            # 进度报告
            if epoch % 10 == 0 or epoch == 1:
                print(f"  轮次 {epoch:3d}/{args.epochs}: "
                      f"损失={epoch_metrics['total_loss']:.6f}, "
                      f"数据={epoch_metrics['data_loss']:.6f}, "
                      f"PDE={epoch_metrics['pde_loss']:.6f}, "
                      f"物理={epoch_metrics['physics_loss']:.6f}")
            
            # 早停
            if patience_counter >= patience_limit:
                print(f"  在第 {epoch} 轮早停 (耐心值超限)")
                break

    print(f"✅ 训练完成！最佳模型已保存: {model_save_path}")

    # 测试
    print(f"🧪 进行不确定性量化测试...")
    model.load_state_dict(torch.load(model_save_path))
    true_labels, pred_mean, pred_std, violation_rate = model.predict_with_uncertainty(
        test_loader, n_samples=args.n_samples
    )

    # 计算指标
    mse = np.mean((pred_mean.flatten() - true_labels.flatten()) ** 2)
    mae = np.mean(np.abs(pred_mean.flatten() - true_labels.flatten()))
    r2 = 1 - np.sum((true_labels.flatten() - pred_mean.flatten()) ** 2) / np.sum(
        (true_labels.flatten() - np.mean(true_labels)) ** 2)

    print(f"📈 AHNU数据结果:")
    print(f"  - MSE: {mse:.8f}")
    print(f"  - MAE: {mae:.8f}")
    print(f"  - R²:  {r2:.6f}")
    print(f"  - SOH违规率: {violation_rate:.4f}")
    
    # 与基线模型对比（参考truly_fixed_full_pinn_test.py）
    print(f"\n📀 与基线模型对比:")
    print(f"  基线MLP:     MSE=0.00029146, MAE=0.01167682, R²=0.965473")
    print(f"  DeepONet-BNN: MSE={mse:.8f}, MAE={mae:.8f}, R²={r2:.6f}")
    
    # 性能评价
    if r2 > 0.9:
        print(f"✅ DeepONet-BNN模型表现良好！")
    elif r2 > 0.8:
        print(f"⚠️  DeepONet-BNN模型表现一般，但优于随机预测")
    elif r2 > 0:
        print(f"🟡 DeepONet-BNN模型表现尚可，需要优化")
    else:
        print(f"❌ DeepONet-BNN模型表现较差，可能存在过拟合")
        print(f"💡 建议: 进一步减少模型复杂度或增加数据量")

    # 保存结果
    np.save(os.path.join(results_dir, "ahnu_true_labels.npy"), true_labels)
    np.save(os.path.join(results_dir, "ahnu_pred_mean.npy"), pred_mean)
    np.save(os.path.join(results_dir, "ahnu_pred_std.npy"), pred_std)

    # 生成图表
    plot_save_path = os.path.join(plots_dir, 'ahnu_soh_prediction_results.png')
    plot_ahnu_results(true_labels, pred_mean, pred_std, plot_save_path, violation_rate)

    print(f"\n🎉 AHNU DeepONet-BNN-PINN v3 完成！")
    print(f"📁 结果保存在: {results_dir}")
    print(f"📊 图表保存在: {plots_dir}")
    print(f"\n💡 17维特征提取策略:")
    print(f"  1-8: 原始电池参数 (充放电容量、效率、电压、时间)")
    print(f"  9-10: 容量比率特征")
    print(f"  11-12: 电压差和时间比特征")
    print(f"  13-14: 效率变换特征")
    print(f"  15-16: 循环相关特征")
    print(f"  17: 综合健康指标")
    print(f"\n📊 SOH计算方法: SOH = 当前放电容量 / 初始放电容量")