import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt
import os

# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# 基线模型对比
# ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

def load_ahnu_data():
    """加载AHNU数据"""
    data_path = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
    df = pd.read_excel(data_path)
    
    print(f"数据形状: {df.shape}")
    
    # 提取最简单的特征
    features = []
    for _, row in df.iterrows():
        # 只用3个最相关的特征
        feature_vec = [
            row['放电比容量(mAh/g)'],     # 最相关
            row['循环号'],                # 时间信息
            row['充放电效率(%)']          # 效率信息
        ]
        features.append(feature_vec)
    
    features = np.array(features)
    
    # SOH计算
    discharge_capacity = df['放电比容量(mAh/g)'].values
    initial_capacity = discharge_capacity[0]
    soh = discharge_capacity / initial_capacity
    soh = np.clip(soh, 0.1, 1.2)
    
    # 标准化特征
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features)
    
    print(f"特征形状: {features_scaled.shape}")
    print(f"SOH范围: [{soh.min():.4f}, {soh.max():.4f}]")
    
    return features_scaled, soh

def compare_baseline_models():
    """对比基线模型"""
    
    print("🔬 基线模型对比实验")
    print("="*50)
    
    # 加载数据
    X, y = load_ahnu_data()
    
    # 划分数据 - 和PINN模型保持一致
    # 时间序列数据，按时间顺序划分
    train_size = int(len(X) * 0.8)
    
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    print(f"训练集: {len(X_train)}, 测试集: {len(X_test)}")
    
    models = {}
    results = {}
    
    # 1. 线性回归
    print("\n📊 1. 线性回归")
    lr = LinearRegression()
    lr.fit(X_train, y_train)
    y_pred_lr = lr.predict(X_test)
    
    mse_lr = np.mean((y_test - y_pred_lr) ** 2)
    mae_lr = np.mean(np.abs(y_test - y_pred_lr))
    r2_lr = 1 - np.sum((y_test - y_pred_lr) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
    
    results['Linear Regression'] = {'MSE': mse_lr, 'MAE': mae_lr, 'R2': r2_lr}
    models['Linear Regression'] = (lr, y_pred_lr)
    
    print(f"  MSE: {mse_lr:.6f}, MAE: {mae_lr:.6f}, R²: {r2_lr:.6f}")
    
    # 2. 随机森林
    print("\n🌲 2. 随机森林")
    rf = RandomForestRegressor(n_estimators=100, max_depth=5, random_state=42)
    rf.fit(X_train, y_train)
    y_pred_rf = rf.predict(X_test)
    
    mse_rf = np.mean((y_test - y_pred_rf) ** 2)
    mae_rf = np.mean(np.abs(y_test - y_pred_rf))
    r2_rf = 1 - np.sum((y_test - y_pred_rf) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
    
    results['Random Forest'] = {'MSE': mse_rf, 'MAE': mae_rf, 'R2': r2_rf}
    models['Random Forest'] = (rf, y_pred_rf)
    
    print(f"  MSE: {mse_rf:.6f}, MAE: {mae_rf:.6f}, R²: {r2_rf:.6f}")
    
    # 3. 简单MLP  
    print("\n🧠 3. 简单MLP")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    class SimpleMLP(nn.Module):
        def __init__(self):
            super().__init__()
            self.network = nn.Sequential(
                nn.Linear(3, 16),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(16, 8),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(8, 1),
                nn.Sigmoid()
            )
        
        def forward(self, x):
            return self.network(x)
    
    # 转换为PyTorch张量
    X_train_torch = torch.FloatTensor(X_train).to(device)
    y_train_torch = torch.FloatTensor(y_train).unsqueeze(1).to(device)
    X_test_torch = torch.FloatTensor(X_test).to(device)
    
    # 训练简单MLP
    mlp = SimpleMLP().to(device)
    optimizer = torch.optim.Adam(mlp.parameters(), lr=0.01, weight_decay=0.01)
    criterion = nn.MSELoss()
    
    # 训练
    mlp.train()
    for epoch in range(200):
        optimizer.zero_grad()
        outputs = mlp(X_train_torch)
        loss = criterion(outputs, y_train_torch)
        loss.backward()
        optimizer.step()
        
        if epoch % 50 == 0:
            print(f"    轮次 {epoch}: 损失 = {loss.item():.6f}")
    
    # 测试
    mlp.eval()
    with torch.no_grad():
        y_pred_mlp = mlp(X_test_torch).cpu().numpy().flatten()
    
    mse_mlp = np.mean((y_test - y_pred_mlp) ** 2)
    mae_mlp = np.mean(np.abs(y_test - y_pred_mlp))
    r2_mlp = 1 - np.sum((y_test - y_pred_mlp) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2)
    
    results['Simple MLP'] = {'MSE': mse_mlp, 'MAE': mae_mlp, 'R2': r2_mlp}
    models['Simple MLP'] = (mlp, y_pred_mlp)
    
    print(f"  MSE: {mse_mlp:.6f}, MAE: {mae_mlp:.6f}, R²: {r2_mlp:.6f}")
    
    # 4. 对比之前的复杂PINN结果
    print(f"\n🤖 4. 之前的PINN结果 (参考)")
    print(f"  v3 (复杂): MSE=0.002444, MAE=0.048967, R²=-25.34")
    print(f"  v4 (简化): MSE=0.001209, MAE=0.034681, R²=-12.03")
    
    # 结果总结
    print(f"\n📊 结果总结")
    print("="*50)
    
    best_model = None
    best_r2 = float('-inf')
    
    for name, metrics in results.items():
        print(f"{name:15s}: MSE={metrics['MSE']:.6f}, MAE={metrics['MAE']:.6f}, R²={metrics['R2']:+.4f}")
        
        if metrics['R2'] > best_r2:
            best_r2 = metrics['R2']
            best_model = name
    
    print(f"\n🏆 最佳模型: {best_model} (R² = {best_r2:.4f})")
    
    # 绘制对比图
    plot_comparison(y_test, models, results)
    
    return results

def plot_comparison(y_true, models, results):
    """绘制模型对比图"""
    
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    colors = ['blue', 'green', 'red']
    
    for i, (name, (model, y_pred)) in enumerate(models.items()):
        ax = axes[i]
        
        # 时间序列对比
        cycles = range(len(y_true))
        ax.plot(cycles, y_true, label='True SOH', color='black', linewidth=2)
        ax.plot(cycles, y_pred, label=f'{name}', color=colors[i], linewidth=2)
        
        ax.set_xlabel('Test Sample Index')
        ax.set_ylabel('SOH')
        ax.set_title(f'{name} (R²={results[name]["R2"]:.4f})')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 第四个子图：对比所有模型的R²
    ax = axes[3]
    model_names = list(results.keys())
    r2_scores = [results[name]['R2'] for name in model_names]
    
    bars = ax.bar(model_names, r2_scores, color=colors)
    ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax.set_ylabel('R² Score')
    ax.set_title('Model Comparison (R² Scores)')
    ax.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, score in zip(bars, r2_scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{score:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图片
    save_dir = './neural_operator_experiment/plots_baseline'
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, 'baseline_comparison.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"\n📊 对比图已保存: {save_path}")

def analyze_data_issues():
    """分析数据本身的问题"""
    
    print(f"\n🔍 数据问题分析")
    print("="*40)
    
    X, y = load_ahnu_data()
    
    # 1. 数据变化幅度
    y_range = y.max() - y.min()
    y_std = y.std()
    print(f"SOH范围: [{y.min():.4f}, {y.max():.4f}]")
    print(f"SOH变化幅度: {y_range:.4f}")
    print(f"SOH标准差: {y_std:.4f}")
    
    # 2. 相邻变化
    y_diff = np.abs(np.diff(y))
    avg_change = np.mean(y_diff)
    print(f"平均相邻变化: {avg_change:.6f}")
    
    # 3. 趋势分析
    from scipy import stats
    slope, intercept, r_value, p_value, std_err = stats.linregress(range(len(y)), y)
    print(f"整体趋势斜率: {slope:.6f}")
    print(f"趋势相关性: {r_value:.4f}")
    
    # 4. 噪声分析
    # 计算去趋势后的残差
    trend = slope * np.arange(len(y)) + intercept
    residuals = y - trend
    noise_level = np.std(residuals)
    signal_level = np.std(trend - np.mean(trend))
    snr = signal_level / noise_level if noise_level > 0 else float('inf')
    
    print(f"信噪比 (SNR): {snr:.2f}")
    
    if avg_change < 0.001:
        print("⚠️  相邻变化过小，时序关系难以学习")
    
    if snr < 3:
        print("⚠️  信噪比过低，信号被噪声掩盖")
    
    if y_range < 0.1:
        print("⚠️  数据变化范围过小")

if __name__ == "__main__":
    
    # 运行基线模型对比
    results = compare_baseline_models()
    
    # 分析数据问题
    analyze_data_issues()
    
    print(f"\n💡 结论和建议:")
    print("="*50)
    
    # 找到最佳基线模型
    best_r2 = max(results[name]['R2'] for name in results)
    
    if best_r2 > 0:
        print(f"✅ 基线模型可以获得正R²，说明问题是可解的")
        print(f"🔧 建议: 简化PINN模型，向最佳基线模型学习")
    else:
        print(f"❌ 所有模型R²都为负，说明数据本身存在问题")
        print(f"🔧 建议: 重新审视特征工程和数据预处理")
        
    print(f"\n🎯 优化方向:")
    print(f"  1. 使用更少但更相关的特征")
    print(f"  2. 简化模型架构")
    print(f"  3. 加强正则化")
    print(f"  4. 考虑数据平滑或去噪")
    print(f"  5. 检查是否存在数据标注问题")