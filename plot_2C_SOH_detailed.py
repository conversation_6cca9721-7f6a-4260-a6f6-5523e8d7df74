import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib import rcParams

# Set Arial font and style
rcParams['font.family'] = 'Arial'
rcParams['font.size'] = 12
plt.style.use('default')

def plot_2C_SOH_detailed():
    """
    Plot detailed SOH vs Cycles curves for all 2C batteries with statistics
    """
    # Data directory
    data_dir = 'data/XJTU data'
    
    # Get all 2C battery files
    battery_files = []
    for i in range(1, 9):
        file_path = os.path.join(data_dir, f'2C_battery-{i}.csv')
        if os.path.exists(file_path):
            battery_files.append((file_path, i))
    
    # Create subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # Nominal capacity for XJTU dataset (2.0 Ah)
    nominal_capacity = 2.0
    
    # Colors for different batteries
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    all_soh_data = []
    battery_stats = []
    
    # Plot 1: Individual SOH curves
    for idx, (file_path, battery_num) in enumerate(battery_files):
        try:
            # Read CSV file
            df = pd.read_csv(file_path)
            
            # Extract capacity and convert to SOH
            capacity = df.iloc[:, -1].values
            soh = capacity / nominal_capacity
            cycles = np.arange(1, len(soh) + 1)
            
            # Store data for statistics
            all_soh_data.append(soh)
            battery_stats.append({
                'battery': battery_num,
                'cycles': len(cycles),
                'initial_soh': soh[0],
                'final_soh': soh[-1],
                'degradation': soh[0] - soh[-1],
                'degradation_rate': (soh[0] - soh[-1]) / len(cycles) * 1000  # per 1000 cycles
            })
            
            # Plot individual curve
            ax1.plot(cycles, soh, 
                    color=colors[idx % len(colors)], 
                    linewidth=2.5, 
                    label=f'Battery {battery_num}',
                    alpha=0.8)
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    # Customize first subplot
    ax1.set_xlabel('Cycle Number', fontsize=14, fontweight='bold')
    ax1.set_ylabel('State of Health (SOH)', fontsize=14, fontweight='bold')
    ax1.set_title('SOH Degradation Curves for 2C Batteries (XJTU Dataset)', fontsize=16, fontweight='bold')
    ax1.set_xlim(0, 450)
    ax1.set_ylim(0.75, 1.0)
    ax1.grid(True, alpha=0.3, linestyle='--')
    ax1.legend(bbox_to_anchor=(1.02, 1), loc='upper left', frameon=True)
    
    # Add 80% SOH threshold line
    ax1.axhline(y=0.8, color='red', linestyle='--', linewidth=2, alpha=0.7, label='80% SOH Threshold')
    
    # Plot 2: Statistics comparison
    battery_nums = [stat['battery'] for stat in battery_stats]
    cycles_count = [stat['cycles'] for stat in battery_stats]
    degradation_rates = [stat['degradation_rate'] for stat in battery_stats]
    
    bars = ax2.bar(battery_nums, degradation_rates, 
                   color=colors[:len(battery_nums)], 
                   alpha=0.7, 
                   edgecolor='black', 
                   linewidth=1)
    
    # Add value labels on bars
    for bar, rate in zip(bars, degradation_rates):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{rate:.2f}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_xlabel('Battery Number', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Degradation Rate (SOH/1000 cycles)', fontsize=14, fontweight='bold')
    ax2.set_title('SOH Degradation Rate Comparison', fontsize=16, fontweight='bold')
    ax2.set_xticks(battery_nums)
    ax2.grid(True, alpha=0.3, linestyle='--', axis='y')
    
    # Add average line
    avg_rate = np.mean(degradation_rates)
    ax2.axhline(y=avg_rate, color='red', linestyle='-', linewidth=2, alpha=0.8, 
                label=f'Average: {avg_rate:.2f}')
    ax2.legend()
    
    plt.tight_layout()
    
    # Save plots
    plt.savefig('2C_SOH_detailed_analysis.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig('2C_SOH_detailed_analysis.pdf', bbox_inches='tight', facecolor='white')
    
    # Print statistics
    print("\n" + "="*60)
    print("2C BATTERY SOH DEGRADATION STATISTICS")
    print("="*60)
    print(f"{'Battery':<8} {'Cycles':<8} {'Initial SOH':<12} {'Final SOH':<11} {'Degradation':<12} {'Rate/1000cyc':<12}")
    print("-"*60)
    
    for stat in battery_stats:
        print(f"{stat['battery']:<8} {stat['cycles']:<8} {stat['initial_soh']:<12.3f} "
              f"{stat['final_soh']:<11.3f} {stat['degradation']:<12.3f} {stat['degradation_rate']:<12.2f}")
    
    print("-"*60)
    avg_cycles = np.mean([stat['cycles'] for stat in battery_stats])
    avg_degradation = np.mean([stat['degradation'] for stat in battery_stats])
    print(f"{'Average':<8} {avg_cycles:<8.0f} {'-':<12} {'-':<11} {avg_degradation:<12.3f} {avg_rate:<12.2f}")
    print("="*60)
    
    plt.show()
    print(f"\nDetailed analysis saved as '2C_SOH_detailed_analysis.png' and '2C_SOH_detailed_analysis.pdf'")

if __name__ == "__main__":
    plot_2C_SOH_detailed()