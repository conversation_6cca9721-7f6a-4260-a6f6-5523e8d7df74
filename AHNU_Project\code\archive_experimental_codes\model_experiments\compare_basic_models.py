#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AHNU数据集基础模型对比测试
对比MLP、CNN和PINN模型在AHNU数据上的性能
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from Model.Model import MLP as BaseMLP, Solution_u
from Model.Compare_Models import MLP, CNN

device = 'cuda' if torch.cuda.is_available() else 'cpu'

class AHNUDataProcessor:
    """AHNU数据处理器 - 简化版本，专注于基础模型测试"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.scaler = StandardScaler()
        self.nominal_capacity = 1200.0
        
    def load_and_process_data(self):
        """加载并处理AHNU数据"""
        print(f"📊 加载AHNU数据: {self.file_path}")
        
        # 读取Excel文件
        df = pd.read_excel(self.file_path)
        print(f"原始数据形状: {df.shape}")
        print(f"数据列: {list(df.columns)}")
        
        # 提取17维特征
        features_17d = self._extract_17_features(df)
        
        # 计算SOH标签
        soh_labels = self._calculate_soh(df)
        
        return features_17d, soh_labels
    
    def _extract_17_features(self, df):
        """从AHNU数据提取17维统计特征"""
        features = []
        
        # 选择关键的电池参数列进行特征提取
        key_columns = [
            '充电比容量(mAh/g)', '放电比容量(mAh/g)', '充放电效率(%)',
            '中值电压(V)', '充电时间(h)', '放电时间(h)', 
            '充电平均电压(V)', '放电平均电压(V)'
        ]
        
        for _, row in df.iterrows():
            row_features = []
            
            # 1-8: 各列的当前值
            for col in key_columns:
                row_features.append(row[col])
            
            # 9-10: 容量相关比率
            charge_cap = row['充电比容量(mAh/g)']
            discharge_cap = row['放电比容量(mAh/g)']
            row_features.append(charge_cap / self.nominal_capacity)  # 充电容量比
            row_features.append(discharge_cap / self.nominal_capacity)  # 放电容量比
            
            # 11-12: 电压差和时间比
            voltage_diff = row['充电平均电压(V)'] - row['放电平均电压(V)']
            time_ratio = row['充电时间(h)'] / (row['放电时间(h)'] + 1e-6)
            row_features.append(voltage_diff)
            row_features.append(time_ratio)
            
            # 13-14: 效率相关特征
            efficiency = row['充放电效率(%)']
            row_features.append(efficiency / 100.0)  # 标准化效率
            row_features.append(np.log(efficiency + 1))  # 对数变换效率
            
            # 15-16: 循环相关特征
            cycle_num = row['循环号']
            row_features.append(cycle_num / 216.0)  # 标准化循环号
            row_features.append(np.sqrt(cycle_num))  # 平方根变换
            
            # 17: 综合健康指标
            health_indicator = (discharge_cap * efficiency / 100.0) / self.nominal_capacity
            row_features.append(health_indicator)
            
            features.append(row_features)
        
        features_array = np.array(features)
        print(f"提取的17维特征形状: {features_array.shape}")
        
        # 标准化特征
        features_array = self.scaler.fit_transform(features_array)
        
        return features_array
    
    def _calculate_soh(self, df):
        """计算SOH值"""
        # 使用放电容量计算SOH
        discharge_capacity = df['放电比容量(mAh/g)'].values
        
        # 相对于初始容量的SOH
        initial_capacity = discharge_capacity[0]
        soh_values = discharge_capacity / initial_capacity
        
        # 确保SOH在合理范围内
        soh_values = np.clip(soh_values, 0.1, 1.2)
        
        print(f"SOH值范围: [{soh_values.min():.4f}, {soh_values.max():.4f}]")
        return soh_values.reshape(-1, 1)

def train_model(model, train_loader, valid_loader, epochs=200, lr=1e-3):
    """训练模型"""
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.8, patience=10, verbose=True
    )
    criterion = nn.MSELoss()
    
    best_valid_loss = float('inf')
    patience_counter = 0
    patience_limit = 20
    
    train_losses = []
    valid_losses = []
    
    print(f"开始训练，总轮数: {epochs}")
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        valid_loss = 0.0
        with torch.no_grad():
            for data, target in valid_loader:
                data, target = data.to(device), target.to(device)
                output = model(data)
                valid_loss += criterion(output, target).item()
        
        avg_valid_loss = valid_loss / len(valid_loader)
        valid_losses.append(avg_valid_loss)
        
        # 学习率调度
        scheduler.step(avg_valid_loss)
        
        # 早停检查
        if avg_valid_loss < best_valid_loss:
            best_valid_loss = avg_valid_loss
            patience_counter = 0
            # 保存最佳模型
            best_model_state = model.state_dict().copy()
        else:
            patience_counter += 1
        
        # 进度报告
        if (epoch + 1) % 20 == 0 or epoch == 0:
            print(f"轮次 {epoch+1:3d}/{epochs}: "
                  f"训练损失={avg_train_loss:.6f}, "
                  f"验证损失={avg_valid_loss:.6f}")
        
        # 早停
        if patience_counter >= patience_limit:
            print(f"在第 {epoch+1} 轮早停")
            break
    
    # 加载最佳模型
    model.load_state_dict(best_model_state)
    
    return train_losses, valid_losses

def evaluate_model(model, test_loader):
    """评估模型"""
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            all_predictions.append(output.cpu().numpy())
            all_targets.append(target.cpu().numpy())
    
    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)
    
    # 计算指标
    mse = mean_squared_error(targets, predictions)
    mae = mean_absolute_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    
    # SOH违规率检查
    violations = np.sum((predictions < 0.1) | (predictions > 1.2))
    violation_rate = violations / len(predictions)
    
    return {
        'mse': mse,
        'mae': mae,
        'r2': r2,
        'violation_rate': violation_rate,
        'predictions': predictions,
        'targets': targets
    }

def main():
    """主函数"""
    print("🚀 AHNU数据集基础模型对比测试")
    print(f"设备: {device}")
    
    # 数据文件路径
    data_file = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
    
    # 加载数据
    processor = AHNUDataProcessor(data_file)
    features, labels = processor.load_and_process_data()
    
    # 转换为张量
    X = torch.from_numpy(features).float()
    y = torch.from_numpy(labels).float()
    
    print(f"特征形状: {X.shape}, 标签形状: {y.shape}")
    
    # 划分数据集
    X_train, X_temp, y_train, y_temp = train_test_split(
        X, y, test_size=0.4, random_state=42
    )
    X_valid, X_test, y_valid, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=42
    )
    
    print(f"训练集: {len(X_train)}, 验证集: {len(X_valid)}, 测试集: {len(X_test)}")
    
    # 创建数据加载器
    batch_size = 32
    train_loader = DataLoader(TensorDataset(X_train, y_train), batch_size=batch_size, shuffle=True)
    valid_loader = DataLoader(TensorDataset(X_valid, y_valid), batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(TensorDataset(X_test, y_test), batch_size=batch_size, shuffle=False)
    
    # 定义模型
    models = {
        'MLP': MLP().to(device),
        'CNN': CNN().to(device),
        'Solution_u': Solution_u().to(device),
    }
    
    results = {}
    
    print(f"\n{'='*60}")
    print("🔋 开始模型训练和评估")
    print(f"{'='*60}")
    
    for model_name, model in models.items():
        print(f"\n🏃 训练 {model_name} 模型...")
        
        # 计算参数数量
        param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"模型参数数量: {param_count:,}")
        
        # 训练模型
        train_losses, valid_losses = train_model(model, train_loader, valid_loader)
        
        # 评估模型
        print(f"🧪 评估 {model_name} 模型...")
        result = evaluate_model(model, test_loader)
        results[model_name] = result
        
        print(f"📈 {model_name} 结果:")
        print(f"  - MSE: {result['mse']:.8f}")
        print(f"  - MAE: {result['mae']:.8f}")
        print(f"  - R²:  {result['r2']:.6f}")
        print(f"  - SOH违规率: {result['violation_rate']:.4f}")
    
    # 结果对比
    print(f"\n{'='*60}")
    print("📊 模型对比结果")
    print(f"{'='*60}")
    
    print(f"{'模型名称':<12} {'MSE':<12} {'MAE':<12} {'R²':<12} {'违规率':<8}")
    print("-" * 60)
    
    for model_name, result in results.items():
        print(f"{model_name:<12} {result['mse']:<12.8f} {result['mae']:<12.8f} "
              f"{result['r2']:<12.6f} {result['violation_rate']:<8.4f}")
    
    # 找出最佳模型
    best_model_r2 = max(results.items(), key=lambda x: x[1]['r2'])
    best_model_mse = min(results.items(), key=lambda x: x[1]['mse'])
    
    print(f"\n🏆 最佳R²模型: {best_model_r2[0]} (R²={best_model_r2[1]['r2']:.6f})")
    print(f"🏆 最低MSE模型: {best_model_mse[0]} (MSE={best_model_mse[1]['mse']:.8f})")
    
    print(f"\n💡 分析:")
    print(f"  - 基础MLP/CNN模型通常更简单，参数更少")
    print(f"  - 如果基础模型表现更好，说明v3版本可能存在过拟合或复杂度过高问题")
    print(f"  - 负的R²值表明模型预测比简单均值预测还要差")

if __name__ == "__main__":
    main()