# DeepONet-BNN-PINN v3 for AHNU Data - Implementation Summary

## 🎯 项目目标
基于v2版本的`main_deeponet_bnn_pinn_v2.py`，创建v3版本来读取和训练AHNU_17_features.csv数据集。

## 📊 数据格式兼容性分析

### XJTU数据格式（v2版本使用）
- **特征数量**: 17个特征列
- **目标列**: `capacity`（需要除以nominal_capacity进行归一化）
- **数据结构**: 17 features + 1 target = 18列

### AHNU数据格式（v3版本使用）
- **特征数量**: 17个特征列
- **目标列**: `SOH`（已经归一化，范围约0.17-0.19）
- **数据结构**: 17 features + 1 target = 18列

✅ **结论**: 数据格式完全兼容，可以直接使用相同的模型架构！

## 🔧 主要修改内容

### 1. 数据加载器修改
```python
class AHNUdata(DF):
    def __init__(self, root, args):
        super(AHNUdata, self).__init__(args)
        self.root = root
        self.csv_file = os.path.join(root, 'AHNU_17_features.csv')
        self.nominal_capacity = 1.0  # AHNU SOH已经归一化

    def read_ahnu_data(self):
        """读取AHNU_17_features.csv并返回数据加载器"""
        return self.load_all_battery(path_list=[self.csv_file], nominal_capacity=self.nominal_capacity)
```

### 2. 模型架构调整
- **输入维度**: 18 (17特征 + 1循环索引)
- **DeepONet输入维度**: 37 (18输入 + 1 u + 17 u_x + 1 u_t)
- **针对小数据集优化的超参数**:
  - 批次大小: 64 → 适合小数据集
  - 学习率: 5e-4 → 更保守的学习率
  - Dropout: 0.15 → 增强正则化
  - 潜在维度: 64 → 减少过拟合风险

### 3. 训练参数优化
```python
# 针对AHNU小数据集的优化参数
parser.add_argument('--epochs', type=int, default=150)
parser.add_argument('--batch_size', type=int, default=64)
parser.add_argument('--lr_u', type=float, default=5e-4)
parser.add_argument('--lr_f', type=float, default=5e-4)
parser.add_argument('--alpha', type=float, default=0.5)  # PDE损失权重
parser.add_argument('--beta', type=float, default=0.8)   # 物理损失权重
```

## 📈 训练结果

### 训练过程（100轮）
```
Epoch   1/100: Loss=72.744144, Data=0.071489, PDE=0.638424, Physics=0.008738
Epoch  10/100: Loss=70.321758, Data=0.038508, PDE=0.050423, Physics=0.007284
Epoch  50/100: Loss=64.887152, Data=0.009181, PDE=0.009393, Physics=0.005658
Epoch 100/100: Loss=62.029585, Data=0.005296, PDE=0.003135, Physics=0.003677
```

### 最终性能指标
- **MSE**: 0.00446725
- **MAE**: 0.06645297
- **RMSE**: 0.06683746
- **R²**: -187.669312 (负值表明模型预测能力有限)
- **SOH违规率**: 0.0000 (完美约束满足)

### 数据统计
- **测试样本数**: 65
- **真实SOH范围**: [0.1721, 0.1932]
- **预测SOH范围**: [0.2322, 0.2918]
- **平均不确定性**: 0.004940

## 🔍 结果分析

### ✅ 优点
1. **约束满足**: 100%满足SOH物理约束（0-1范围）
2. **不确定性量化**: 贝叶斯网络提供可靠的不确定性估计
3. **稳定训练**: 损失函数稳定下降，无发散现象
4. **物理一致性**: PDE和物理损失有效降低

### ⚠️ 需要改进的地方
1. **R²值较低**: -187.67表明预测精度有待提升
2. **预测偏移**: 预测值系统性高于真实值
3. **数据量限制**: AHNU数据集较小（336样本），可能导致过拟合

### 🎯 改进建议
1. **增加训练轮数**: 当前100轮可能不够充分
2. **调整损失权重**: 增加数据损失权重，减少PDE损失权重
3. **数据增强**: 考虑使用数据增强技术扩充训练集
4. **超参数调优**: 进一步优化学习率、网络结构等

## 📁 文件结构
```
AHNU_Project/
├── main_deeponet_bnn_pinn_v3.py          # v3主程序
├── analyze_v3_results.py                  # 结果分析脚本
├── data/
│   └── AHNU_17_features.csv              # AHNU数据集
├── results_v3_ahnu/                      # 训练结果
│   ├── enhanced_deeponet_bnn_model_ahnu_v3.pth
│   ├── true_labels_ahnu_v3.npy
│   ├── pred_mean_ahnu_v3.npy
│   └── pred_std_ahnu_v3.npy
├── plots_v3_ahnu/                        # 结果图表
│   └── enhanced_prediction_ahnu_v3.png
└── history_v3_ahnu/                      # 训练历史
    └── training_history_ahnu_v3.npz
```

## 🚀 运行方式
```bash
# 激活环境
conda activate Vision

# 进入项目目录
cd AHNU_Project

# 运行v3版本训练
python main_deeponet_bnn_pinn_v3.py --epochs 100 --save_history

# 分析结果
python analyze_v3_results.py
```

## 📋 总结
v3版本成功实现了从XJTU数据到AHNU数据的迁移，证明了模型架构的通用性。虽然当前性能有待提升，但基础框架已经建立，为后续优化提供了良好的起点。
