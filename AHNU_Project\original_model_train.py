"""
使用原始Model代码训练AHNU数据
Train AHNU data using original Model code
"""
import sys
import os
import torch
import numpy as np
import pandas as pd
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split

# 添加路径以导入原始Model
sys.path.append('../')
from Model.Model import PINN, Solution_u, MLP
from utils.util import AverageMeter, get_logger, eval_metrix

# 复制原始dataloader的DF类
class DF():
    def __init__(self, args):
        self.normalization = True
        self.normalization_method = args.normalization_method
        self.args = args

    def _3_sigma(self, Ser1):
        rule = (Ser1.mean() - 3 * Ser1.std() > Ser1) | (Ser1.mean() + 3 * Ser1.std() < Ser1)
        index = np.arange(Ser1.shape[0])[rule]
        return index

    def delete_3_sigma(self, df):
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.dropna()
        df = df.reset_index(drop=True)
        out_index = []
        for col in df.columns:
            index = self._3_sigma(df[col])
            out_index.extend(index)
        out_index = list(set(out_index))
        df = df.drop(out_index, axis=0)
        df = df.reset_index(drop=True)
        return df

    def read_one_csv(self, file_name, nominal_capacity=None):
        """
        读取CSV文件并按照原始Model的方式处理
        """
        df = pd.read_csv(file_name)
        
        # 关键步骤：在倒数第二列插入cycle index（与原始Model完全一致）
        df.insert(df.shape[1]-1, 'cycle index', np.arange(df.shape[0]))
        
        df = self.delete_3_sigma(df)

        if nominal_capacity is not None:
            # 计算SOH：capacity/nominal_capacity（与原始Model一致）
            df['capacity'] = df['capacity'] / nominal_capacity
            
            # 归一化特征（除了最后一列capacity）
            f_df = df.iloc[:, :-1]
            if self.normalization_method == 'min-max':
                f_df = 2 * (f_df - f_df.min()) / (f_df.max() - f_df.min()) - 1
            elif self.normalization_method == 'z-score':
                f_df = (f_df - f_df.mean()) / f_df.std()

            df.iloc[:, :-1] = f_df

        return df

    def load_one_battery(self, path, nominal_capacity=None):
        df = self.read_one_csv(path, nominal_capacity)
        x = df.iloc[:, :-1].values  # 16特征 + 1cycle_index = 17维
        y = df.iloc[:, -1].values   # capacity (SOH)
        x1 = x[:-1]
        x2 = x[1:]
        y1 = y[:-1]
        y2 = y[1:]
        return (x1, y1), (x2, y2)

    def load_all_battery(self, path_list, nominal_capacity):
        X1, X2, Y1, Y2 = [], [], [], []
        
        for path in path_list:
            (x1, y1), (x2, y2) = self.load_one_battery(path, nominal_capacity)
            X1.append(x1)
            X2.append(x2)
            Y1.append(y1)
            Y2.append(y2)

        X1 = np.concatenate(X1, axis=0)
        X2 = np.concatenate(X2, axis=0)
        Y1 = np.concatenate(Y1, axis=0)
        Y2 = np.concatenate(Y2, axis=0)

        tensor_X1 = torch.from_numpy(X1).float()
        tensor_X2 = torch.from_numpy(X2).float()
        tensor_Y1 = torch.from_numpy(Y1).float().view(-1, 1)
        tensor_Y2 = torch.from_numpy(Y2).float().view(-1, 1)

        # 数据划分
        split = int(tensor_X1.shape[0] * 0.8)
        train_X1, test_X1 = tensor_X1[:split], tensor_X1[split:]
        train_X2, test_X2 = tensor_X2[:split], tensor_X2[split:]
        train_Y1, test_Y1 = tensor_Y1[:split], tensor_Y1[split:]
        train_Y2, test_Y2 = tensor_Y2[:split], tensor_Y2[split:]
        
        train_X1, valid_X1, train_X2, valid_X2, train_Y1, valid_Y1, train_Y2, valid_Y2 = \
            train_test_split(train_X1, train_X2, train_Y1, train_Y2, test_size=0.2, random_state=420)

        train_loader = DataLoader(TensorDataset(train_X1, train_X2, train_Y1, train_Y2),
                                  batch_size=self.args.batch_size, shuffle=True)
        valid_loader = DataLoader(TensorDataset(valid_X1, valid_X2, valid_Y1, valid_Y2),
                                  batch_size=self.args.batch_size, shuffle=True)
        test_loader = DataLoader(TensorDataset(test_X1, test_X2, test_Y1, test_Y2),
                                 batch_size=self.args.batch_size, shuffle=False)

        loader = {'train': train_loader, 'valid': valid_loader, 'test': test_loader}
        return loader

class AHNUdata(DF):
    def __init__(self, root, args):
        super(AHNUdata, self).__init__(args)
        self.root = root
        self.csv_file = os.path.join(root, 'AHNU_for_original_model.csv')
        
        # 设置nominal_capacity用于SOH计算
        # AHNU数据的capacity范围是[0.1721, 1.1141]，我们使用最大值作为nominal_capacity
        self.nominal_capacity = 1.1141  # 或者使用1.0作为标准化基准
        
        print(f"📊 AHNU数据配置:")
        print(f"   - 数据文件: {self.csv_file}")
        print(f"   - Nominal capacity: {self.nominal_capacity}")

    def read_ahnu_data(self):
        """读取AHNU数据"""
        if not os.path.exists(self.csv_file):
            raise FileNotFoundError(f"AHNU数据文件不存在: {self.csv_file}")
        
        print(f"📂 加载AHNU数据: {self.csv_file}")
        return self.load_all_battery(path_list=[self.csv_file], nominal_capacity=self.nominal_capacity)

class MockArgs:
    """模拟参数类，与原始Model保持一致"""
    def __init__(self):
        # 数据参数
        self.batch_size = 64
        self.normalization_method = 'z-score'
        
        # 模型参数
        self.F_layers_num = 3
        self.F_hidden_dim = 60
        
        # 训练参数
        self.epochs = 100
        self.lr = 1e-3
        self.warmup_epochs = 10
        self.warmup_lr = 5e-4
        self.final_lr = 1e-4
        self.lr_F = 1e-3
        self.iter_per_epoch = 1
        
        # 损失权重
        self.alpha = 1.0  # PDE loss weight
        self.beta = 1.0   # Physics loss weight
        
        # 日志和保存
        self.save_folder = './results_original_model'
        self.log_dir = 'training.log'
        self.early_stop = 20

def main():
    print("🚀 使用原始Model训练AHNU数据")
    print("=" * 50)
    
    # 创建参数
    args = MockArgs()
    
    # 创建保存目录
    os.makedirs(args.save_folder, exist_ok=True)
    
    print(f"📊 训练配置:")
    print(f"   - 批次大小: {args.batch_size}")
    print(f"   - 训练轮数: {args.epochs}")
    print(f"   - 学习率: {args.lr}")
    print(f"   - PDE损失权重: {args.alpha}")
    print(f"   - 物理损失权重: {args.beta}")
    
    # 加载数据
    data_loader = AHNUdata(root='./data', args=args)
    loaders = data_loader.read_ahnu_data()
    train_loader = loaders['train']
    valid_loader = loaders['valid']
    test_loader = loaders['test']
    
    print(f"📊 数据统计:")
    print(f"   - 训练批次: {len(train_loader)}")
    print(f"   - 验证批次: {len(valid_loader)}")
    print(f"   - 测试批次: {len(test_loader)}")
    
    # 创建模型
    model = PINN(args)
    
    print(f"🏗️ 模型架构:")
    print(f"   - Solution_u: 17 → 32 → 1")
    print(f"   - dynamical_F: 35 → {args.F_hidden_dim} → 1")
    
    # 开始训练
    print(f"\n🏋️ 开始训练...")
    model.Train(trainloader=train_loader, validloader=valid_loader, testloader=test_loader)
    
    print(f"✅ 训练完成!")
    print(f"📁 结果保存在: {args.save_folder}")

if __name__ == "__main__":
    main()
