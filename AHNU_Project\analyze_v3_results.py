import numpy as np
import matplotlib.pyplot as plt
import scienceplots

# Load results
true_labels = np.load('./results_v3_ahnu/true_labels_ahnu_v3.npy')
pred_mean = np.load('./results_v3_ahnu/pred_mean_ahnu_v3.npy')
pred_std = np.load('./results_v3_ahnu/pred_std_ahnu_v3.npy')

# Calculate metrics
mse = np.mean((pred_mean.flatten() - true_labels.flatten()) ** 2)
mae = np.mean(np.abs(pred_mean.flatten() - true_labels.flatten()))
r2 = 1 - np.sum((true_labels.flatten() - pred_mean.flatten()) ** 2) / np.sum((true_labels.flatten() - np.mean(true_labels)) ** 2)

print("🔍 AHNU DeepONet-BNN-PINN v3 Results Analysis")
print("=" * 50)
print(f"📊 Dataset Statistics:")
print(f"  - Total samples: {len(true_labels)}")
print(f"  - True SOH range: [{true_labels.min():.4f}, {true_labels.max():.4f}]")
print(f"  - Predicted SOH range: [{pred_mean.min():.4f}, {pred_mean.max():.4f}]")
print(f"  - Mean uncertainty: {np.mean(pred_std):.6f}")

print(f"\n📈 Performance Metrics:")
print(f"  - MSE: {mse:.8f}")
print(f"  - MAE: {mae:.8f}")
print(f"  - R²:  {r2:.6f}")
print(f"  - RMSE: {np.sqrt(mse):.8f}")

# Check for violations
violations = ((pred_mean < 0) | (pred_mean > 1)).sum()
print(f"\n⚠️  SOH Constraint Violations:")
print(f"  - Violations: {violations}/{len(pred_mean)} ({violations/len(pred_mean)*100:.2f}%)")

# Analyze prediction quality
errors = np.abs(pred_mean.flatten() - true_labels.flatten())
print(f"\n🎯 Error Analysis:")
print(f"  - Mean absolute error: {np.mean(errors):.6f}")
print(f"  - Std of errors: {np.std(errors):.6f}")
print(f"  - Max error: {np.max(errors):.6f}")
print(f"  - 95th percentile error: {np.percentile(errors, 95):.6f}")

print(f"\n🔬 Uncertainty Analysis:")
print(f"  - Mean uncertainty: {np.mean(pred_std):.6f}")
print(f"  - Std of uncertainty: {np.std(pred_std):.6f}")
print(f"  - Max uncertainty: {np.max(pred_std):.6f}")

# Create a simple comparison plot
plt.style.use(['science', 'nature'])
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5), dpi=150)

# Plot 1: Predictions vs True
ax1.plot(true_labels, label='True SOH', color='blue', linewidth=2)
ax1.plot(pred_mean, label='Predicted SOH', color='red', linewidth=2)
ax1.fill_between(range(len(pred_mean)), 
                 (pred_mean - 2*pred_std).flatten(),
                 (pred_mean + 2*pred_std).flatten(),
                 alpha=0.3, color='red', label='95% CI')
ax1.set_xlabel('Sample Index')
ax1.set_ylabel('SOH')
ax1.set_title('AHNU SOH Prediction Results')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot 2: Scatter plot
ax2.scatter(true_labels.flatten(), pred_mean.flatten(), alpha=0.6, s=10)
min_val = min(true_labels.min(), pred_mean.min())
max_val = max(true_labels.max(), pred_mean.max())
ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
ax2.set_xlabel('True SOH')
ax2.set_ylabel('Predicted SOH')
ax2.set_title(f'True vs Predicted (R² = {r2:.3f})')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('./plots_v3_ahnu/analysis_summary.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\n✅ Analysis complete! Summary plot saved to: ./plots_v3_ahnu/analysis_summary.png")
