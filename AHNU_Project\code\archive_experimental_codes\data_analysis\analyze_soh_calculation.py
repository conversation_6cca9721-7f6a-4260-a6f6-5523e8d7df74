#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析AHNU数据集中SOH的计算步骤
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import scienceplots
plt.style.use(['science', 'nature'])
import os

def analyze_soh_calculation():
    """分析SOH计算步骤"""
    print("🔍 分析AHNU数据集中的SOH计算步骤")
    print("="*60)
    
    # 数据文件路径
    data_file = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
    
    # 读取数据
    df = pd.read_excel(data_file)
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名: {list(df.columns)}")
    
    # 显示前几行数据
    print(f"\n📈 前10行数据:")
    print(df.head(10))
    
    # 分析放电容量列
    discharge_capacity_col = '放电比容量(mAh/g)'
    print(f"\n🔋 放电容量分析:")
    print(f"  列名: {discharge_capacity_col}")
    print(f"  数据类型: {df[discharge_capacity_col].dtype}")
    print(f"  范围: [{df[discharge_capacity_col].min():.2f}, {df[discharge_capacity_col].max():.2f}]")
    print(f"  均值: {df[discharge_capacity_col].mean():.2f}")
    print(f"  标准差: {df[discharge_capacity_col].std():.2f}")
    
    # 计算SOH值
    print(f"\n🧮 SOH计算步骤:")
    print(f"  1. 选择放电容量列: '{discharge_capacity_col}'")
    discharge_capacity = df[discharge_capacity_col].values
    print(f"  2. 获取初始容量(第1个循环): {discharge_capacity[0]:.2f} mAh/g")
    initial_capacity = discharge_capacity[0]
    print(f"  3. 计算相对SOH值: SOH = 当前容量 / 初始容量")
    soh_values = discharge_capacity / initial_capacity
    print(f"  4. 限制SOH范围在[0.1, 1.2]之间")
    soh_values = np.clip(soh_values, 0.1, 1.2)
    print(f"  5. 最终SOH范围: [{soh_values.min():.4f}, {soh_values.max():.4f}]")
    
    # 显示前10个SOH值
    print(f"\n📈 前10个SOH值:")
    for i in range(min(10, len(soh_values))):
        print(f"  循环 {i+1}: 容量={discharge_capacity[i]:.2f} -> SOH={soh_values[i]:.4f}")
    
    # 绘制SOH变化图
    cycles = df['循环号'].values
    plt.figure(figsize=(8, 4), dpi=200)
    plt.plot(cycles, soh_values, 'o-', markersize=3, linewidth=1)
    plt.xlabel('Cycle Number')
    plt.ylabel('SOH')
    plt.title('SOH Degradation Curve for AHNU Dataset')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # 保存图片
    save_path = './AHNU_Project/results/soh_degradation_curve.png'
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"\n📁 SOH退化曲线已保存到: {save_path}")
    
    # 显示统计信息
    print(f"\n📊 SOH统计信息:")
    print(f"  - 最大值: {soh_values.max():.4f}")
    print(f"  - 最小值: {soh_values.min():.4f}")
    print(f"  - 均值: {soh_values.mean():.4f}")
    print(f"  - 标准差: {soh_values.std():.4f}")
    
    # 检查SOH约束
    violations_low = np.sum(soh_values < 0.1)
    violations_high = np.sum(soh_values > 1.2)
    print(f"\n⚠️  SOH约束检查:")
    print(f"  - 低于0.1的值: {violations_low} 个")
    print(f"  - 高于1.2的值: {violations_high} 个")
    
    return soh_values

def main():
    """主函数"""
    soh_values = analyze_soh_calculation()
    print(f"\n✅ SOH计算分析完成!")

if __name__ == "__main__":
    main()