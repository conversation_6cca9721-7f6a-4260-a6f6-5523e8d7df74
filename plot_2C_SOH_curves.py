import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib import rcParams

# Set Arial font
rcParams['font.family'] = 'Arial'
rcParams['font.size'] = 12

def plot_2C_SOH_curves():
    """
    Plot SOH vs Cycles curves for all 2C batteries in XJTU dataset
    """
    # Data directory
    data_dir = 'data/XJTU data'
    
    # Get all 2C battery files
    battery_files = []
    for i in range(1, 9):  # 2C_battery-1.csv to 2C_battery-8.csv
        file_path = os.path.join(data_dir, f'2C_battery-{i}.csv')
        if os.path.exists(file_path):
            battery_files.append(file_path)
    
    # Create figure
    plt.figure(figsize=(12, 8))
    
    # Nominal capacity for XJTU dataset (2.0 Ah)
    nominal_capacity = 2.0
    
    # Colors for different batteries
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    for idx, file_path in enumerate(battery_files):
        try:
            # Read CSV file
            df = pd.read_csv(file_path)
            
            # Extract capacity (last column) and convert to SOH
            capacity = df.iloc[:, -1].values
            soh = capacity / nominal_capacity  # Convert to SOH (State of Health)
            
            # Create cycle index
            cycles = np.arange(1, len(soh) + 1)
            
            # Plot SOH curve
            battery_name = f'Battery {idx + 1}'
            plt.plot(cycles, soh, 
                    color=colors[idx % len(colors)], 
                    linewidth=2, 
                    label=battery_name,
                    alpha=0.8)
            
            print(f"Battery {idx + 1}: {len(cycles)} cycles, SOH range: {soh.min():.3f} - {soh.max():.3f}")
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    # Customize plot
    plt.xlabel('Cycle Number', fontsize=14, fontweight='bold')
    plt.ylabel('State of Health (SOH)', fontsize=14, fontweight='bold')
    plt.title('SOH vs Cycles for 2C Batteries (XJTU Dataset)', fontsize=16, fontweight='bold')
    
    # Set axis limits and grid
    plt.xlim(0, None)
    plt.ylim(0.7, 1.05)
    plt.grid(True, alpha=0.3, linestyle='--')
    
    # Add legend
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', frameon=True, fancybox=True, shadow=True)
    
    # Tight layout to prevent legend cutoff
    plt.tight_layout()
    
    # Save plot
    plt.savefig('2C_SOH_curves.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig('2C_SOH_curves.pdf', bbox_inches='tight', facecolor='white')
    
    # Show plot
    plt.show()
    
    print("\nPlot saved as '2C_SOH_curves.png' and '2C_SOH_curves.pdf'")

if __name__ == "__main__":
    plot_2C_SOH_curves()