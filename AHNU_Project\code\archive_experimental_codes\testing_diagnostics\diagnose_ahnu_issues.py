#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import sys
import os
sys.path.append('./neural_operator_experiment')

from main_deeponet_bnn_pinn_v3 import AHNUDataLoader, get_ahnu_args

def diagnose_ahnu_model_issues():
    """诊断AHNU模型训练问题"""
    
    print("🔍 AHNU模型问题诊断")
    print("="*60)
    
    # 加载参数
    class Args:
        data_file = r'E:\gitrepo\PINN4SOH\AHNU_Project\data\0.1-0.5A g-1 <EMAIL>'
        batch_size = 32
        normalization_method = 'z-score'
    
    args = Args()
    
    # 1. 数据分析
    print("\n📊 1. 数据量分析")
    print("-" * 40)
    
    data_loader = AHNUDataLoader(args.data_file, args)
    loaders = data_loader.get_dataloaders()
    
    train_size = len(loaders['train'].dataset)
    valid_size = len(loaders['valid'].dataset) 
    test_size = len(loaders['test'].dataset)
    
    print(f"训练集: {train_size} 样本")
    print(f"验证集: {valid_size} 样本")
    print(f"测试集: {test_size} 样本")
    print(f"总计: {train_size + valid_size + test_size} 样本")
    
    # 判断数据量是否足够
    if train_size < 200:
        print("⚠️  训练样本过少，建议至少500+样本用于复杂PINN模型")
    
    # 2. SOH数据分布分析
    print("\n🔋 2. SOH数据分布分析")
    print("-" * 40)
    
    # 加载一个batch查看数据
    train_batch = next(iter(loaders['train']))
    x1, x2, y1, y2 = train_batch
    
    print(f"特征范围:")
    print(f"  X1: [{x1.min().item():.4f}, {x1.max().item():.4f}]")
    print(f"  X2: [{x2.min().item():.4f}, {x2.max().item():.4f}]")
    
    print(f"SOH范围:")
    print(f"  Y1: [{y1.min().item():.4f}, {y1.max().item():.4f}]")  
    print(f"  Y2: [{y2.min().item():.4f}, {y2.max().item():.4f}]")
    
    # 检查SOH变化幅度
    soh_diff = torch.abs(y2 - y1).mean()
    print(f"平均SOH变化: {soh_diff.item():.6f}")
    
    if soh_diff < 0.001:
        print("⚠️  SOH变化太小，模型难以学习时序关系")
    
    # 3. 特征相关性分析
    print("\n📈 3. 特征相关性分析")  
    print("-" * 40)
    
    # 合并所有数据
    all_x, all_y = [], []
    for x1, x2, y1, y2 in loaders['train']:
        all_x.append(x1)
        all_x.append(x2)
        all_y.append(y1)
        all_y.append(y2)
    
    all_x = torch.cat(all_x, dim=0).numpy()
    all_y = torch.cat(all_y, dim=0).numpy()
    
    # 计算特征与SOH的相关性
    correlations = []
    for i in range(all_x.shape[1]):
        corr = np.corrcoef(all_x[:, i], all_y.flatten())[0, 1]
        correlations.append(corr)
    
    # 显示最相关的特征
    sorted_idx = np.argsort(np.abs(correlations))[::-1]
    
    print("特征与SOH相关性排序 (前10):")
    for i, idx in enumerate(sorted_idx[:10]):
        print(f"  特征{idx+1:2d}: {correlations[idx]:+.4f}")
    
    # 检查是否有高相关性特征
    max_corr = max([abs(c) for c in correlations if not np.isnan(c)])
    print(f"最高相关性: {max_corr:.4f}")
    
    if max_corr < 0.3:
        print("⚠️  所有特征与SOH相关性都很低，特征工程需要改进")
    
    # 4. 模型复杂度分析
    print("\n🏗️  4. 模型复杂度分析")
    print("-" * 40)
    
    # 估算参数数量
    # BNN Solution Network: 17->40->60->40->32->24->16->1
    solution_params = 17*40 + 40*60 + 60*40 + 40*32 + 32*24 + 24*16 + 16*1
    
    # DeepONet: Branch(20->64->128->64) + Trunk(1->32->64->32) + Output(1->16->1)  
    deeponet_params = 20*64 + 64*128 + 128*64 + 1*32 + 32*64 + 64*32 + 1*16 + 16*1
    
    total_params = solution_params + deeponet_params
    
    print(f"估算参数数量:")
    print(f"  Solution网络: ~{solution_params:,} 参数")
    print(f"  DeepONet网络: ~{deeponet_params:,} 参数") 
    print(f"  总计: ~{total_params:,} 参数")
    
    # 参数与数据比
    param_data_ratio = total_params / train_size
    print(f"参数/训练样本比: {param_data_ratio:.1f}")
    
    if param_data_ratio > 100:
        print("⚠️  模型过于复杂，严重过拟合风险")
    elif param_data_ratio > 10:
        print("⚠️  模型复杂度较高，需要强正则化")
    
    # 5. 损失权重分析
    print("\n⚖️  5. 损失权重分析")
    print("-" * 40)
    
    print("当前权重设置:")
    print("  数据损失: 1.0 (基准)")
    print("  PDE损失: 0.5 (alpha)")
    print("  物理损失: 0.8 (beta)")
    print("  SOH约束: 5.0") 
    print("  贝叶斯KL: 1e-4")
    
    print("\n从训练日志看:")
    print("  - 约束损失=0.0 → SOH约束未激活")
    print("  - PDE损失很小 → DeepONet学习不足")
    print("  - 物理损失适中 → 物理约束有效")
    print("  - 总损失过高 → 数据拟合困难")
    
    # 6. 建议改进方案
    print("\n💡 6. 建议改进方案")
    print("-" * 40)
    
    print("🎯 优先级改进:")
    print("  1. 简化模型架构 - 减少参数数量")
    print("  2. 增强特征工程 - 提高特征与SOH相关性")  
    print("  3. 调整损失权重 - 平衡各项损失")
    print("  4. 数据增强 - 增加训练样本")
    print("  5. 预训练策略 - 分阶段训练")
    
    print("\n🔧 具体技术方案:")
    print("  - 减小网络层数和神经元数量")
    print("  - 增加dropout和权重衰减")
    print("  - 使用更简单的SOH约束")
    print("  - 降低学习率，增加训练轮数")
    print("  - 考虑使用更简单的MLP基线模型")
    
    return {
        'train_size': train_size,
        'max_correlation': max_corr,
        'param_data_ratio': param_data_ratio,
        'soh_change': soh_diff.item()
    }

if __name__ == "__main__":
    results = diagnose_ahnu_model_issues()
    
    print("\n🏁 诊断总结:")
    print("="*60)
    
    # 问题严重程度评估
    issues = []
    
    if results['train_size'] < 200:
        issues.append("数据量不足")
    
    if results['max_correlation'] < 0.3:
        issues.append("特征相关性低")
        
    if results['param_data_ratio'] > 50:
        issues.append("模型过复杂")
        
    if results['soh_change'] < 0.001:
        issues.append("SOH变化微小")
    
    if len(issues) == 0:
        print("✅ 未发现重大问题")
    else:
        print(f"❌ 发现 {len(issues)} 个主要问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    
    print(f"\n建议: 优先解决模型复杂度和特征工程问题")